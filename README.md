# CTF 擂台前端项目

一个基于 Vue 3 + TypeScript 的现代化 CTF（Capture The Flag）擂台前端应用，提供完整的用户认证、挑战管理、比赛参与和个人数据统计功能。

## 🚀 项目特性

### 核心功能
- **用户认证系统** - 完整的登录/注册功能，支持状态持久化
- **挑战管理** - 多分类CTF挑战题目，支持Flag提交和验证
- **比赛系统** - CTF比赛信息展示和报名功能
- **个人中心** - 详细的统计数据、进度追踪和活动记录
- **响应式设计** - 完美适配桌面端和移动端

### 技术特性
- **现代化技术栈** - Vue 3 + TypeScript + Pinia + SCSS
- **组合式API** - 使用Vue 3 Composition API
- **状态管理** - Pinia状态管理，支持数据持久化
- **类型安全** - 完整的TypeScript类型定义
- **响应式布局** - 移动优先的响应式设计

## 🛠️ 技术栈

- **前端框架**: Vue 3.4.29
- **开发语言**: TypeScript 5.2.2
- **状态管理**: Pinia 2.1.7
- **路由管理**: Vue Router 4.3.3
- **样式预处理**: SCSS
- **构建工具**: Vite 5.4.19
- **包管理器**: npm

## 📦 安装和运行

### 环境要求
- Node.js 16.0+
- npm 7.0+

### 安装依赖
```bash
npm install
```

### 开发环境运行
```bash
npm run dev
```

### 生产环境构建
```bash
npm run build
```

### 预览生产构建
```bash
npm run preview
```

## 🏗️ 项目结构

```
src/
├── components/          # 公共组件
│   └── Navbar.vue      # 导航栏组件
├── views/              # 页面组件
│   ├── Home.vue        # 首页
│   ├── Auth.vue        # 认证页面
│   ├── Challenges.vue  # 挑战页面
│   ├── Competitions.vue # 比赛页面
│   └── Profile.vue     # 个人资料页面
├── router/             # 路由配置
│   └── index.ts        # 路由定义和守卫
├── store/              # 状态管理
│   └── index.ts        # Pinia stores
├── style.css           # 全局样式
└── main.ts            # 应用入口
```

## 🎨 功能详解

### 1. 用户认证系统
- **智能表单验证** - 实时邮箱格式和密码强度检查
- **状态持久化** - 自动保存登录状态到localStorage
- **路由守卫** - 保护需要认证的页面
- **错误处理** - 友好的错误提示和恢复机制

### 2. 挑战管理系统
- **多分类支持** - Web安全、密码学、逆向工程、二进制漏洞、取证分析
- **难度分级** - 简单、中等、困难三个难度等级
- **Flag验证** - 模拟真实的Flag提交和验证流程
- **进度追踪** - 实时记录解题状态和时间

### 3. 比赛系统
- **比赛信息** - 详细的比赛信息展示
- **报名功能** - 支持团队报名和成员管理
- **状态筛选** - 按状态和格式筛选比赛
- **时间管理** - 智能的时间显示和状态更新

### 4. 个人数据中心
- **实时统计** - 基于实际数据的统计计算
- **分类进度** - 各技术分类的完成情况
- **活动记录** - 详细的解题历史和时间线
- **可视化展示** - 进度条和图表展示

## 🎯 设计理念

### 用户体验优先
- **直观导航** - 清晰的页面结构和导航逻辑
- **即时反馈** - 操作的即时视觉反馈
- **流畅动画** - 适度的过渡动画增强体验
- **错误友好** - 友好的错误提示和处理

### 响应式设计
- **移动优先** - 基于移动端的设计策略
- **渐进增强** - 桌面端的功能和视觉增强
- **断点设计** - 768px、1200px响应式断点
- **触摸优化** - 移动端的触摸交互优化

### 现代化界面
- **毛玻璃效果** - backdrop-filter模糊效果
- **渐变色彩** - 科技感的渐变色方案
- **微动画** - 悬停和点击的微交互
- **深色主题** - 适合长时间使用的深色界面

## 🔧 开发指南

### 状态管理
项目使用Pinia进行状态管理，主要包含：

```typescript
// 认证状态
const authStore = useAuthStore()
authStore.login(userData)
authStore.logout()

// 挑战状态
const challengeStore = useChallengeStore()
challengeStore.submitFlag(challengeId, flag)
challengeStore.setSelectedCategory(category)
```

### 路由守卫
```typescript
// 保护需要认证的路由
{
  path: '/profile',
  meta: { requiresAuth: true }
}
```

### 组件开发
- 使用Composition API
- TypeScript类型定义
- SCSS模块化样式
- 响应式设计原则

## 🎨 样式规范

### 颜色方案
- **主色调**: #00ff88 (霓虹绿)
- **辅助色**: #00a3ff (科技蓝)
- **背景色**: #0a0a0a (深黑)
- **文本色**: #ffffff (纯白)

### 字体规范
- **移动端**: 16px 基础字体
- **桌面端**: 18px 基础字体
- **标题层级**: 1.1rem - 4.5rem
- **字体族**: 系统默认字体栈

### 间距规范
- **基础间距**: 0.5rem, 1rem, 1.5rem, 2rem
- **组件内边距**: 1.2rem - 2rem
- **页面内边距**: 2rem - 4rem
- **网格间距**: 1.5rem - 2.5rem

## 📱 响应式断点

| 设备类型 | 屏幕宽度 | 设计特点 |
|---------|---------|----------|
| 移动端 | < 768px | 单列布局，紧凑设计 |
| 平板端 | 768px - 1199px | 多列布局，适中间距 |
| 桌面端 | ≥ 1200px | 宽屏布局，丰富交互 |

## 📋 项目开发历程

### 1. 汉化工作 ✅
- **导航栏汉化** - 所有导航链接和按钮文本
- **页面内容汉化** - 首页、认证、挑战、比赛、个人资料页面
- **表单和错误信息汉化** - 完整的中文用户界面
- **日期格式本地化** - 中文日期格式显示

### 2. 桌面端适配 ✅
- **响应式设计** - 支持移动端、平板端、桌面端
- **布局优化** - 充分利用大屏幕空间
- **交互增强** - 桌面端的悬停效果和动画
- **字体和间距优化** - 适配不同屏幕尺寸

### 3. 功能完善 ✅
- **路由守卫** - 认证保护和自动重定向
- **状态管理重构** - Pinia状态管理和数据持久化
- **认证系统优化** - 表单验证、加载状态、错误处理
- **挑战系统升级** - Flag提交验证、进度追踪
- **个人中心重构** - 实时统计、分类进度、活动记录

### 4. 代码优化 ✅
- **SCSS语法修复** - 解决编译错误和样式冲突
- **尺寸优化** - 减少过大的内边距和字体
- **性能优化** - CSS结构优化和渲染性能提升
- **代码质量** - 统一代码风格和最佳实践

## 🔒 安全考虑

### 前端安全
- **输入验证** - 客户端表单验证
- **XSS防护** - 安全的数据绑定
- **状态保护** - 安全的状态管理

### 数据保护
- **本地存储** - 合理使用localStorage
- **敏感信息** - 避免存储敏感数据
- **用户隐私** - 保护用户隐私信息

## 🚀 部署指南

### 构建优化
```bash
# 生产环境构建
npm run build

# 构建产物在 dist/ 目录
```

### 服务器配置
- **静态文件服务** - 配置静态文件服务器
- **路由回退** - 配置SPA路由回退
- **HTTPS** - 建议使用HTTPS协议
- **CDN** - 可配置CDN加速

## 🔮 扩展计划

### 功能扩展
- **国际化支持** - 多语言切换
- **主题切换** - 明暗主题切换
- **实时通信** - WebSocket实时功能
- **文件上传** - 支持文件上传功能

### 技术升级
- **PWA支持** - 渐进式Web应用
- **服务端渲染** - SSR/SSG支持
- **微前端** - 模块化架构
- **性能监控** - 性能监控和分析

## 📊 性能指标

### 构建产物
- **JavaScript**: ~200KB (gzipped)
- **CSS**: ~50KB (gzipped)
- **首屏加载**: < 2s
- **交互响应**: < 100ms

### 浏览器支持
- **Chrome**: 60+
- **Firefox**: 55+
- **Safari**: 12+
- **Edge**: 79+

## 📄 许可证

MIT License

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

### 开发流程
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

### 代码规范
- 遵循 TypeScript 最佳实践
- 使用 ESLint 和 Prettier
- 编写清晰的提交信息
- 添加必要的测试

---

**CTF 擂台前端项目** - 为网络安全学习者打造的现代化平台 🛡️
