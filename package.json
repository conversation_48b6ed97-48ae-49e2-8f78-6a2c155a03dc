{"name": "ctf-battle-platform", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@tsparticles/vue3": "^3.0.1", "axios": "^1.6.2", "chart.js": "^4.4.0", "crypto-js": "^4.1.1", "date-fns": "^2.30.0", "pinia": "^2.1.7", "tsparticles": "^3.0.2", "vue": "^3.4.38", "vue-chartjs": "^5.2.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/crypto-js": "^4.1.3", "@vitejs/plugin-vue": "^5.1.3", "sass": "^1.89.0", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.1.4"}}