<script setup lang="ts">
import { useAuthStore } from '../store'
import { computed } from 'vue'

const authStore = useAuthStore()
const isAuthenticated = computed(() => authStore.isAuthenticated)
</script>

<template>
  <nav class="navbar">
    <router-link to="/" class="logo">CTF Platform</router-link>
    
    <div class="nav-links">
      <router-link to="/challenges">Challenges</router-link>
      <router-link to="/competitions">Competitions</router-link>
      <template v-if="isAuthenticated">
        <router-link to="/profile">Profile</router-link>
        <a @click="authStore.logout" class="logout">Logout</a>
      </template>
      <template v-else>
        <router-link to="/auth" class="login">Login</router-link>
      </template>
    </div>
  </nav>
</template>

<style scoped lang="scss">
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 1000;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #00ff88;
  text-decoration: none;
}

.nav-links {
  display: flex;
  gap: 2rem;
  align-items: center;

  a {
    color: white;
    text-decoration: none;
    transition: color 0.3s;

    &:hover {
      color: #00ff88;
    }

    &.login {
      padding: 0.5rem 1.5rem;
      border: 2px solid #00ff88;
      border-radius: 25px;
      color: #00ff88;

      &:hover {
        background: #00ff88;
        color: black;
      }
    }
  }
}

.logout {
  cursor: pointer;
}
</style>