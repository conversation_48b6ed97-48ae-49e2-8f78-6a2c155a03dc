<script setup lang="ts">
import { useAuthStore } from '../store'
import { computed } from 'vue'

const authStore = useAuthStore()
const isAuthenticated = computed(() => authStore.isAuthenticated)
</script>

<template>
  <nav class="navbar">
    <router-link to="/" class="logo">AI 攻防推演</router-link>

    <div class="nav-links">
      <router-link to="/scenario">场景配置</router-link>
      <router-link to="/simulation">实时推演</router-link>
      <router-link to="/control">AI 控制台</router-link>
      <router-link to="/reports">评估报告</router-link>
      <template v-if="isAuthenticated">
        <router-link to="/profile">个人中心</router-link>
        <a @click="authStore.logout" class="logout">退出登录</a>
      </template>
      <template v-else>
        <router-link to="/auth" class="login">登录</router-link>
      </template>
    </div>
  </nav>
</template>

<style scoped lang="scss">
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 1000;
  height: 70px;
  box-sizing: border-box;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: #00ff88;
  text-decoration: none;
}

.nav-links {
  display: flex;
  gap: 2rem;
  align-items: center;

  a {
    color: white;
    text-decoration: none;
    transition: color 0.3s;
    font-size: 1rem;
    font-weight: 500;

    &:hover {
      color: #00ff88;
    }

    &.login {
      padding: 0.5rem 1.5rem;
      border: 2px solid #00ff88;
      border-radius: 25px;
      color: #00ff88;

      &:hover {
        background: #00ff88;
        color: black;
      }
    }
  }
}

/* 桌面端适配 */
@media (min-width: 768px) {
  .navbar {
    padding: 1.5rem 4rem;
    height: 80px;
  }

  .logo {
    font-size: 2rem;
  }

  .nav-links {
    gap: 3rem;

    a {
      font-size: 1.1rem;

      &.login {
        padding: 0.7rem 2rem;
        font-size: 1rem;
      }
    }
  }
}

@media (min-width: 1200px) {
  .navbar {
    padding: 1.5rem 6rem;
  }

  .nav-links {
    gap: 4rem;
  }
}

.logout {
  cursor: pointer;
}
</style>