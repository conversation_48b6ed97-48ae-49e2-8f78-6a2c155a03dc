import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import { useAuthStore } from './store'
import './style.css'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)

// 初始化应用状态
const initApp = () => {
  const authStore = useAuthStore()

  // 初始化认证状态
  authStore.initAuth()

  // 注意：挑战数据已被AI推演数据替代，无需初始化
}

app.mount('#app')

// 应用挂载后初始化状态
initApp()