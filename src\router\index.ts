import { createRouter, createWebHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Challenges from '../views/Challenges.vue'
import Competitions from '../views/Competitions.vue'
import Profile from '../views/Profile.vue'
import Auth from '../views/Auth.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home
    },
    {
      path: '/challenges',
      name: 'challenges',
      component: Challenges
    },
    {
      path: '/competitions',
      name: 'competitions',
      component: Competitions
    },
    {
      path: '/profile',
      name: 'profile',
      component: Profile
    },
    {
      path: '/auth',
      name: 'auth',
      component: Auth
    }
  ]
})

export default router