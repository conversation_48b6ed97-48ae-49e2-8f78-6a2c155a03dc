import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../store'
import Home from '../views/Home.vue'
import ScenarioConfig from '../views/ScenarioConfig.vue'
import Simulation from '../views/Simulation.vue'
import AIControl from '../views/AIControl.vue'
import Reports from '../views/Reports.vue'
import Profile from '../views/Profile.vue'
import Auth from '../views/Auth.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home,
      meta: { title: 'AI 攻防推演平台 - 首页' }
    },
    {
      path: '/scenario',
      name: 'scenario',
      component: ScenarioConfig,
      meta: {
        title: 'AI 攻防推演平台 - 场景配置',
        requiresAuth: true
      }
    },
    {
      path: '/simulation',
      name: 'simulation',
      component: Simulation,
      meta: {
        title: 'AI 攻防推演平台 - 实时推演',
        requiresAuth: true
      }
    },
    {
      path: '/control',
      name: 'control',
      component: AIControl,
      meta: {
        title: 'AI 攻防推演平台 - AI控制台',
        requiresAuth: true
      }
    },
    {
      path: '/reports',
      name: 'reports',
      component: Reports,
      meta: {
        title: 'AI 攻防推演平台 - 评估报告',
        requiresAuth: true
      }
    },
    {
      path: '/profile',
      name: 'profile',
      component: Profile,
      meta: {
        title: 'AI 攻防推演平台 - 个人中心',
        requiresAuth: true
      }
    },
    {
      path: '/auth',
      name: 'auth',
      component: Auth,
      meta: {
        title: 'AI 攻防推演平台 - 登录注册',
        hideForAuth: true
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'notFound',
      redirect: '/'
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title as string
  }

  // 检查需要认证的路由
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/auth')
    return
  }

  // 已登录用户访问登录页面时重定向
  if (to.meta.hideForAuth && authStore.isAuthenticated) {
    next('/profile')
    return
  }

  next()
})

export default router