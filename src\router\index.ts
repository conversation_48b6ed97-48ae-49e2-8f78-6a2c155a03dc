import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../store'
import Home from '../views/Home.vue'
import Challenges from '../views/Challenges.vue'
import Competitions from '../views/Competitions.vue'
import Profile from '../views/Profile.vue'
import Auth from '../views/Auth.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home,
      meta: { title: 'CTF 擂台 - 首页' }
    },
    {
      path: '/challenges',
      name: 'challenges',
      component: Challenges,
      meta: { title: 'CTF 擂台 - 挑战题目' }
    },
    {
      path: '/competitions',
      name: 'competitions',
      component: Competitions,
      meta: { title: 'CTF 擂台 - 比赛竞技' }
    },
    {
      path: '/profile',
      name: 'profile',
      component: Profile,
      meta: {
        title: 'CTF 擂台 - 个人资料',
        requiresAuth: true
      }
    },
    {
      path: '/auth',
      name: 'auth',
      component: Auth,
      meta: {
        title: 'CTF 擂台 - 登录注册',
        hideForAuth: true
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'notFound',
      redirect: '/'
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title as string
  }

  // 检查需要认证的路由
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/auth')
    return
  }

  // 已登录用户访问登录页面时重定向
  if (to.meta.hideForAuth && authStore.isAuthenticated) {
    next('/profile')
    return
  }

  next()
})

export default router