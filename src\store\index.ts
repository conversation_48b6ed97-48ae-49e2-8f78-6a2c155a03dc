import { defineStore } from 'pinia'

interface User {
  id: string
  email: string
  name: string
  avatar?: string
  joinDate?: string
}

interface Challenge {
  id: number
  title: string
  category: string
  difficulty: string
  points: number
  solved: boolean
  description: string
  solvedAt?: string
}

interface Competition {
  id: number
  title: string
  startDate: string
  endDate: string
  format: 'Jeopardy' | 'Attack/Defense' | 'King of the Hill'
  difficulty: string
  teamSize: string
  prize: string
  registered: boolean
  description: string
  status: 'upcoming' | 'ongoing' | 'completed'
}

// 认证状态管理
export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null as User | null,
    isAuthenticated: false,
    loading: false,
    error: ''
  }),

  getters: {
    userName: (state) => state.user?.name || '游客',
    userEmail: (state) => state.user?.email || '',
    isLoggedIn: (state) => state.isAuthenticated && state.user !== null
  },

  actions: {
    // 登录
    async login(userData: Partial<User>) {
      this.loading = true
      this.error = ''

      try {
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 1000))

        const user: User = {
          id: userData.id || '1',
          email: userData.email || '',
          name: userData.name || userData.email?.split('@')[0] || '用户',
          avatar: userData.avatar,
          joinDate: new Date().toISOString()
        }

        this.user = user
        this.isAuthenticated = true

        // 持久化到localStorage
        localStorage.setItem('auth_user', JSON.stringify(user))
        localStorage.setItem('auth_token', 'mock_token_' + Date.now())

      } catch (error) {
        this.error = '登录失败，请重试'
        throw error
      } finally {
        this.loading = false
      }
    },

    // 注册
    async register(userData: { email: string; password: string; name?: string }) {
      this.loading = true
      this.error = ''

      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1200))

        const user: User = {
          id: 'user_' + Date.now(),
          email: userData.email,
          name: userData.name || userData.email.split('@')[0],
          joinDate: new Date().toISOString()
        }

        this.user = user
        this.isAuthenticated = true

        // 持久化
        localStorage.setItem('auth_user', JSON.stringify(user))
        localStorage.setItem('auth_token', 'mock_token_' + Date.now())

      } catch (error) {
        this.error = '注册失败，请重试'
        throw error
      } finally {
        this.loading = false
      }
    },

    // 登出
    logout() {
      this.user = null
      this.isAuthenticated = false
      this.error = ''

      // 清除持久化数据
      localStorage.removeItem('auth_user')
      localStorage.removeItem('auth_token')
    },

    // 初始化认证状态
    initAuth() {
      const savedUser = localStorage.getItem('auth_user')
      const savedToken = localStorage.getItem('auth_token')

      if (savedUser && savedToken) {
        try {
          this.user = JSON.parse(savedUser)
          this.isAuthenticated = true
        } catch (error) {
          console.error('Failed to parse saved user data:', error)
          this.logout()
        }
      }
    },

    // 清除错误
    clearError() {
      this.error = ''
    }
  }
})

// 挑战状态管理
export const useChallengeStore = defineStore('challenges', {
  state: () => ({
    challenges: [] as Challenge[],
    loading: false,
    error: '',
    selectedCategory: '全部'
  }),

  getters: {
    filteredChallenges: (state) => {
      if (state.selectedCategory === '全部') {
        return state.challenges
      }
      return state.challenges.filter(challenge =>
        challenge.category === state.selectedCategory
      )
    },

    solvedChallenges: (state) => state.challenges.filter(c => c.solved),
    totalPoints: (state) => state.challenges
      .filter(c => c.solved)
      .reduce((sum, c) => sum + c.points, 0),

    challengesByCategory: (state) => {
      const categories = ['Web 安全', '密码学', '逆向工程', '二进制漏洞', '取证分析']
      return categories.map(category => ({
        name: category,
        challenges: state.challenges.filter(c => c.category === category),
        solved: state.challenges.filter(c => c.category === category && c.solved).length
      }))
    }
  },

  actions: {
    // 初始化挑战数据
    initChallenges() {
      this.challenges = [
        {
          id: 1,
          title: 'SQL 注入基础',
          category: 'Web 安全',
          difficulty: '简单',
          points: 100,
          solved: false,
          description: '学习 SQL 注入的基础知识，利用漏洞登录表单。'
        },
        {
          id: 2,
          title: '经典凯撒密码',
          category: '密码学',
          difficulty: '简单',
          points: 50,
          solved: false,
          description: '解密使用凯撒密码编码的消息。'
        },
        {
          id: 3,
          title: 'XSS 反射型攻击',
          category: 'Web 安全',
          difficulty: '中等',
          points: 200,
          solved: false,
          description: '利用反射型 XSS 漏洞获取用户 Cookie。'
        },
        {
          id: 4,
          title: 'RSA 密钥分析',
          category: '密码学',
          difficulty: '困难',
          points: 300,
          solved: false,
          description: '分析弱 RSA 密钥并破解加密消息。'
        },
        {
          id: 5,
          title: '缓冲区溢出',
          category: '二进制漏洞',
          difficulty: '困难',
          points: 400,
          solved: false,
          description: '利用缓冲区溢出漏洞获取系统权限。'
        }
      ]
    },

    // 提交Flag
    async submitFlag(challengeId: number, flag: string) {
      this.loading = true
      this.error = ''

      try {
        // 模拟验证Flag
        await new Promise(resolve => setTimeout(resolve, 800))

        // 简单的Flag验证逻辑
        const correctFlags: { [key: number]: string } = {
          1: 'CTF{sql_injection_basic}',
          2: 'CTF{caesar_cipher_solved}',
          3: 'CTF{xss_reflected_attack}',
          4: 'CTF{rsa_weak_key_broken}',
          5: 'CTF{buffer_overflow_pwned}'
        }

        if (correctFlags[challengeId] === flag) {
          const challenge = this.challenges.find(c => c.id === challengeId)
          if (challenge) {
            challenge.solved = true
            challenge.solvedAt = new Date().toISOString()
          }
          return { success: true, message: 'Flag 正确！挑战完成！' }
        } else {
          return { success: false, message: 'Flag 错误，请重试。' }
        }

      } catch (error) {
        this.error = '提交失败，请重试'
        return { success: false, message: '提交失败，请重试' }
      } finally {
        this.loading = false
      }
    },

    // 设置选中的分类
    setSelectedCategory(category: string) {
      this.selectedCategory = category
    }
  }
})