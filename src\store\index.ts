import { defineStore } from 'pinia'

interface User {
  id: string
  email: string
  name: string
  avatar?: string
  joinDate?: string
}

// AI推演相关接口定义
interface ScenarioConfig {
  id: string
  name: string
  template: string
  topology: string
  businessType: string
  vulnerabilityLevel: string
  customConfig?: string
  createdAt: string
  updatedAt: string
}

interface NetworkNode {
  id: string
  name: string
  ip: string
  type: 'server' | 'firewall' | 'database' | 'attacker' | 'client'
  status: 'normal' | 'compromised' | 'active' | 'attacking' | 'defending'
  x: number
  y: number
  services?: string[]
  vulnerabilities?: string[]
}

interface AIAgentConfig {
  aggressiveness: number
  intelligence: number
  stealth: number
  persistence: number
  strategy: string
}

interface AIAgentStatus {
  status: 'active' | 'paused' | 'monitoring' | 'error'
  currentAction: string
  nextAction: string
  confidence: number
  successRate?: number
  blockRate?: number
}

interface SimulationEvent {
  id: number
  timestamp: string
  type: 'attack' | 'defense'
  subType: string
  source: string
  target: string
  status: 'success' | 'blocked' | 'pending'
  description: string
  confidence?: number
}

interface SimulationSession {
  id: string
  name: string
  scenarioId: string
  startTime: string
  endTime?: string
  status: 'preparing' | 'running' | 'paused' | 'completed'
  currentPhase: string
  duration: number
  events: SimulationEvent[]
  metrics: {
    attackSuccess: number
    defenseEfficiency: number
    systemIntegrity: number
    networkTraffic: number
  }
}

interface DecisionLog {
  id: number
  timestamp: string
  agent: 'attacker' | 'defender'
  decision: string
  reasoning: string
  confidence: number
  result: 'success' | 'blocked' | 'pending'
}

interface AssessmentReport {
  id: string
  sessionId: string
  generatedAt: string
  overview: {
    totalSessions: number
    avgDuration: number
    attackSuccessRate: number
    defenseEfficiency: number
  }
  skillAssessment: {
    [skill: string]: number
  }
  vulnerabilityStats: Array<{
    type: string
    discovered: number
    exploited: number
    blocked: number
  }>
}

// 认证状态管理
export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null as User | null,
    isAuthenticated: false,
    loading: false,
    error: ''
  }),

  getters: {
    userName: (state) => state.user?.name || '游客',
    userEmail: (state) => state.user?.email || '',
    isLoggedIn: (state) => state.isAuthenticated && state.user !== null
  },

  actions: {
    // 登录
    async login(userData: Partial<User>) {
      this.loading = true
      this.error = ''

      try {
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 1000))

        const user: User = {
          id: userData.id || '1',
          email: userData.email || '',
          name: userData.name || userData.email?.split('@')[0] || '用户',
          avatar: userData.avatar,
          joinDate: new Date().toISOString()
        }

        this.user = user
        this.isAuthenticated = true

        // 持久化到localStorage
        localStorage.setItem('auth_user', JSON.stringify(user))
        localStorage.setItem('auth_token', 'mock_token_' + Date.now())

      } catch (error) {
        this.error = '登录失败，请重试'
        throw error
      } finally {
        this.loading = false
      }
    },

    // 注册
    async register(userData: { email: string; password: string; name?: string }) {
      this.loading = true
      this.error = ''

      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1200))

        const user: User = {
          id: 'user_' + Date.now(),
          email: userData.email,
          name: userData.name || userData.email.split('@')[0],
          joinDate: new Date().toISOString()
        }

        this.user = user
        this.isAuthenticated = true

        // 持久化
        localStorage.setItem('auth_user', JSON.stringify(user))
        localStorage.setItem('auth_token', 'mock_token_' + Date.now())

      } catch (error) {
        this.error = '注册失败，请重试'
        throw error
      } finally {
        this.loading = false
      }
    },

    // 登出
    logout() {
      this.user = null
      this.isAuthenticated = false
      this.error = ''

      // 清除持久化数据
      localStorage.removeItem('auth_user')
      localStorage.removeItem('auth_token')
    },

    // 初始化认证状态
    initAuth() {
      const savedUser = localStorage.getItem('auth_user')
      const savedToken = localStorage.getItem('auth_token')

      if (savedUser && savedToken) {
        try {
          this.user = JSON.parse(savedUser)
          this.isAuthenticated = true
        } catch (error) {
          console.error('Failed to parse saved user data:', error)
          this.logout()
        }
      }
    },

    // 清除错误
    clearError() {
      this.error = ''
    }
  }
})

// 场景配置状态管理
export const useScenarioStore = defineStore('scenario', {
  state: () => ({
    scenarios: [] as ScenarioConfig[],
    currentScenario: null as ScenarioConfig | null,
    networkNodes: [] as NetworkNode[],
    loading: false,
    error: '',
    templates: [
      {
        id: 'web_pentest',
        name: 'Web应用渗透测试',
        description: '包含常见Web漏洞的企业应用场景',
        icon: '🌐'
      },
      {
        id: 'network_intrusion',
        name: '网络入侵检测',
        description: '模拟APT攻击的企业网络环境',
        icon: '🔒'
      },
      {
        id: 'cloud_security',
        name: '云安全防护',
        description: '云原生应用的安全防护场景',
        icon: '☁️'
      },
      {
        id: 'iot_security',
        name: 'IoT设备安全',
        description: '物联网设备的安全测试环境',
        icon: '📱'
      }
    ]
  }),

  getters: {
    getScenarioById: (state) => (id: string) => {
      return state.scenarios.find(scenario => scenario.id === id)
    },

    getNodesByType: (state) => (type: string) => {
      return state.networkNodes.filter(node => node.type === type)
    },

    compromisedNodes: (state) => {
      return state.networkNodes.filter(node => node.status === 'compromised')
    }
  },

  actions: {
    // 创建场景配置
    async createScenario(config: Omit<ScenarioConfig, 'id' | 'createdAt' | 'updatedAt'>) {
      this.loading = true
      this.error = ''

      try {
        await new Promise(resolve => setTimeout(resolve, 1000))

        const newScenario: ScenarioConfig = {
          ...config,
          id: 'scenario_' + Date.now(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }

        this.scenarios.push(newScenario)
        this.currentScenario = newScenario

        // 生成默认网络节点
        this.generateDefaultNodes(newScenario)

        return { success: true, scenario: newScenario }
      } catch (error) {
        this.error = '创建场景失败'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    // 生成默认网络节点
    generateDefaultNodes(scenario: ScenarioConfig) {
      const defaultNodes: NetworkNode[] = [
        {
          id: 'web-server',
          name: 'Web服务器',
          ip: '********',
          type: 'server',
          status: 'normal',
          x: 200,
          y: 150,
          services: ['HTTP', 'HTTPS'],
          vulnerabilities: ['SQL注入', 'XSS']
        },
        {
          id: 'db-server',
          name: '数据库服务器',
          ip: '********',
          type: 'database',
          status: 'normal',
          x: 400,
          y: 150,
          services: ['MySQL'],
          vulnerabilities: ['弱密码']
        },
        {
          id: 'firewall',
          name: '防火墙',
          ip: '********',
          type: 'firewall',
          status: 'active',
          x: 100,
          y: 100,
          services: ['Firewall']
        },
        {
          id: 'attacker',
          name: '攻击者',
          ip: '*************',
          type: 'attacker',
          status: 'attacking',
          x: 50,
          y: 200
        }
      ]

      this.networkNodes = defaultNodes
    },

    // 更新场景配置
    async updateScenario(id: string, updates: Partial<ScenarioConfig>) {
      this.loading = true
      this.error = ''

      try {
        await new Promise(resolve => setTimeout(resolve, 500))

        const scenario = this.scenarios.find(s => s.id === id)
        if (scenario) {
          Object.assign(scenario, updates, { updatedAt: new Date().toISOString() })
          if (this.currentScenario?.id === id) {
            this.currentScenario = scenario
          }
        }

        return { success: true }
      } catch (error) {
        this.error = '更新场景失败'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    // 删除场景
    async deleteScenario(id: string) {
      this.loading = true
      this.error = ''

      try {
        await new Promise(resolve => setTimeout(resolve, 500))

        this.scenarios = this.scenarios.filter(s => s.id !== id)
        if (this.currentScenario?.id === id) {
          this.currentScenario = null
        }

        return { success: true }
      } catch (error) {
        this.error = '删除场景失败'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    // 设置当前场景
    setCurrentScenario(scenario: ScenarioConfig | null) {
      this.currentScenario = scenario
      if (scenario) {
        this.generateDefaultNodes(scenario)
      }
    },

    // 更新网络节点状态
    updateNodeStatus(nodeId: string, status: NetworkNode['status']) {
      const node = this.networkNodes.find(n => n.id === nodeId)
      if (node) {
        node.status = status
      }
    },

    // 清除错误
    clearError() {
      this.error = ''
    }
  }
})

// AI控制台状态管理
export const useAIControlStore = defineStore('aiControl', {
  state: () => ({
    attackerConfig: {
      aggressiveness: 70,
      intelligence: 85,
      stealth: 60,
      persistence: 75,
      strategy: 'adaptive'
    } as AIAgentConfig & { strategy: string },

    defenderConfig: {
      alertness: 80,
      responseSpeed: 90,
      coverage: 75,
      adaptability: 65,
      strategy: 'proactive'
    } as AIAgentConfig & { strategy: string, alertness: number, responseSpeed: number, coverage: number, adaptability: number },

    attackerStatus: {
      status: 'active',
      currentAction: '端口扫描',
      nextAction: 'SQL注入尝试',
      confidence: 0.82,
      successRate: 0.65
    } as AIAgentStatus & { successRate: number },

    defenderStatus: {
      status: 'monitoring',
      currentAction: '流量分析',
      nextAction: '异常检测',
      confidence: 0.78,
      blockRate: 0.73
    } as AIAgentStatus & { blockRate: number },

    decisionLogs: [] as DecisionLog[],
    loading: false,
    error: ''
  }),

  getters: {
    attackerEffectiveness: (state) => {
      const { aggressiveness, intelligence, stealth, persistence } = state.attackerConfig
      return Math.round((aggressiveness + intelligence + stealth + persistence) / 4)
    },

    defenderEffectiveness: (state) => {
      const { alertness, responseSpeed, coverage, adaptability } = state.defenderConfig
      return Math.round((alertness + responseSpeed + coverage + adaptability) / 4)
    },

    recentDecisions: (state) => {
      return state.decisionLogs.slice(-10).reverse()
    }
  },

  actions: {
    // 更新攻击者配置
    async updateAttackerConfig(config: Partial<typeof this.attackerConfig>) {
      this.loading = true
      this.error = ''

      try {
        await new Promise(resolve => setTimeout(resolve, 300))

        Object.assign(this.attackerConfig, config)

        // 记录配置变更决策
        this.addDecisionLog({
          agent: 'attacker',
          decision: '更新攻击配置',
          reasoning: `调整参数: ${Object.keys(config).join(', ')}`,
          confidence: 0.95,
          result: 'success'
        })

        return { success: true }
      } catch (error) {
        this.error = '更新攻击者配置失败'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    // 更新防御者配置
    async updateDefenderConfig(config: Partial<typeof this.defenderConfig>) {
      this.loading = true
      this.error = ''

      try {
        await new Promise(resolve => setTimeout(resolve, 300))

        Object.assign(this.defenderConfig, config)

        // 记录配置变更决策
        this.addDecisionLog({
          agent: 'defender',
          decision: '更新防御配置',
          reasoning: `调整参数: ${Object.keys(config).join(', ')}`,
          confidence: 0.95,
          result: 'success'
        })

        return { success: true }
      } catch (error) {
        this.error = '更新防御者配置失败'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    // 暂停AI
    pauseAI(agent: 'attacker' | 'defender') {
      if (agent === 'attacker') {
        this.attackerStatus.status = 'paused'
      } else {
        this.defenderStatus.status = 'paused'
      }

      this.addDecisionLog({
        agent,
        decision: '暂停AI运行',
        reasoning: '用户手动暂停AI操作',
        confidence: 1.0,
        result: 'success'
      })
    },

    // 恢复AI
    resumeAI(agent: 'attacker' | 'defender') {
      if (agent === 'attacker') {
        this.attackerStatus.status = 'active'
      } else {
        this.defenderStatus.status = 'monitoring'
      }

      this.addDecisionLog({
        agent,
        decision: '恢复AI运行',
        reasoning: '用户手动恢复AI操作',
        confidence: 1.0,
        result: 'success'
      })
    },

    // 重置AI
    resetAI(agent: 'attacker' | 'defender') {
      if (agent === 'attacker') {
        this.attackerConfig = {
          aggressiveness: 70,
          intelligence: 85,
          stealth: 60,
          persistence: 75,
          strategy: 'adaptive'
        }
        this.attackerStatus = {
          status: 'active',
          currentAction: '初始化',
          nextAction: '环境扫描',
          confidence: 0.5,
          successRate: 0.0
        }
      } else {
        this.defenderConfig = {
          alertness: 80,
          responseSpeed: 90,
          coverage: 75,
          adaptability: 65,
          strategy: 'proactive'
        }
        this.defenderStatus = {
          status: 'monitoring',
          currentAction: '初始化',
          nextAction: '基线监控',
          confidence: 0.5,
          blockRate: 0.0
        }
      }

      this.addDecisionLog({
        agent,
        decision: '重置AI状态',
        reasoning: '用户手动重置AI到初始状态',
        confidence: 1.0,
        result: 'success'
      })
    },

    // 添加决策日志
    addDecisionLog(log: Omit<DecisionLog, 'id' | 'timestamp'>) {
      const newLog: DecisionLog = {
        ...log,
        id: Date.now(),
        timestamp: new Date().toISOString()
      }

      this.decisionLogs.push(newLog)

      // 限制日志数量
      if (this.decisionLogs.length > 100) {
        this.decisionLogs = this.decisionLogs.slice(-50)
      }
    },

    // 更新AI状态
    updateAIStatus(agent: 'attacker' | 'defender', updates: Partial<AIAgentStatus>) {
      if (agent === 'attacker') {
        Object.assign(this.attackerStatus, updates)
      } else {
        Object.assign(this.defenderStatus, updates)
      }
    },

    // 清除错误
    clearError() {
      this.error = ''
    }
  }
})

// 实时推演状态管理
export const useSimulationStore = defineStore('simulation', {
  state: () => ({
    currentSession: null as SimulationSession | null,
    sessions: [] as SimulationSession[],
    isRunning: false,
    currentTime: 0,
    totalTime: 300,
    currentPhase: '准备阶段',
    events: [] as SimulationEvent[],
    metrics: {
      attackSuccess: 0,
      defenseEfficiency: 100,
      systemIntegrity: 100,
      networkTraffic: 0
    },
    loading: false,
    error: ''
  }),

  getters: {
    recentEvents: (state) => {
      return state.events.slice(-20).reverse()
    },

    attackEvents: (state) => {
      return state.events.filter(e => e.type === 'attack')
    },

    defenseEvents: (state) => {
      return state.events.filter(e => e.type === 'defense')
    },

    sessionDuration: (state) => {
      if (!state.currentSession) return 0
      const start = new Date(state.currentSession.startTime).getTime()
      const end = state.currentSession.endTime
        ? new Date(state.currentSession.endTime).getTime()
        : Date.now()
      return Math.floor((end - start) / 1000)
    }
  },

  actions: {
    // 开始新的推演会话
    async startSession(scenarioId: string, sessionName: string) {
      this.loading = true
      this.error = ''

      try {
        await new Promise(resolve => setTimeout(resolve, 1000))

        const newSession: SimulationSession = {
          id: 'session_' + Date.now(),
          name: sessionName,
          scenarioId,
          startTime: new Date().toISOString(),
          status: 'running',
          currentPhase: '侦察阶段',
          duration: 0,
          events: [],
          metrics: {
            attackSuccess: 0,
            defenseEfficiency: 100,
            systemIntegrity: 100,
            networkTraffic: 0
          }
        }

        this.currentSession = newSession
        this.sessions.push(newSession)
        this.isRunning = true
        this.currentTime = 0
        this.currentPhase = '侦察阶段'
        this.events = []
        this.metrics = { ...newSession.metrics }

        return { success: true, session: newSession }
      } catch (error) {
        this.error = '启动推演失败'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    // 暂停推演
    pauseSession() {
      this.isRunning = false
      if (this.currentSession) {
        this.currentSession.status = 'paused'
      }
    },

    // 恢复推演
    resumeSession() {
      this.isRunning = true
      if (this.currentSession) {
        this.currentSession.status = 'running'
      }
    },

    // 停止推演
    stopSession() {
      this.isRunning = false
      if (this.currentSession) {
        this.currentSession.status = 'completed'
        this.currentSession.endTime = new Date().toISOString()
        this.currentSession.duration = this.sessionDuration
      }
      this.currentPhase = '推演结束'
    },

    // 重置推演
    resetSession() {
      this.isRunning = false
      this.currentTime = 0
      this.currentPhase = '准备阶段'
      this.events = []
      this.metrics = {
        attackSuccess: 0,
        defenseEfficiency: 100,
        systemIntegrity: 100,
        networkTraffic: 0
      }
      this.currentSession = null
    },

    // 添加事件
    addEvent(event: Omit<SimulationEvent, 'id' | 'timestamp'>) {
      const newEvent: SimulationEvent = {
        ...event,
        id: Date.now(),
        timestamp: new Date().toISOString()
      }

      this.events.push(newEvent)

      if (this.currentSession) {
        this.currentSession.events.push(newEvent)
      }

      // 限制事件数量
      if (this.events.length > 100) {
        this.events = this.events.slice(-50)
      }
    },

    // 更新指标
    updateMetrics(updates: Partial<typeof this.metrics>) {
      Object.assign(this.metrics, updates)

      if (this.currentSession) {
        Object.assign(this.currentSession.metrics, updates)
      }
    },

    // 更新推演时间
    updateTime(time: number) {
      this.currentTime = time

      // 更新阶段
      if (time < 60) {
        this.currentPhase = '侦察阶段'
      } else if (time < 180) {
        this.currentPhase = '攻击阶段'
      } else if (time < 240) {
        this.currentPhase = '横向移动'
      } else {
        this.currentPhase = '数据窃取'
      }

      if (this.currentSession) {
        this.currentSession.currentPhase = this.currentPhase
        this.currentSession.duration = time
      }
    },

    // 清除错误
    clearError() {
      this.error = ''
    }
  }
})

// 评估报告状态管理
export const useReportsStore = defineStore('reports', {
  state: () => ({
    reports: [] as AssessmentReport[],
    currentReport: null as AssessmentReport | null,
    timeRange: '7days',
    loading: false,
    error: ''
  }),

  getters: {
    filteredReports: (state) => {
      const now = new Date()
      const days = {
        '7days': 7,
        '30days': 30,
        '90days': 90,
        'all': 365 * 10
      }[state.timeRange] || 7

      const cutoff = new Date(now.getTime() - days * 24 * 60 * 60 * 1000)

      return state.reports.filter(report =>
        new Date(report.generatedAt) >= cutoff
      )
    },

    averageScore: (state) => {
      const reports = state.reports
      if (reports.length === 0) return 0

      const totalScore = reports.reduce((sum, report) => {
        const skills = Object.values(report.skillAssessment)
        const avgSkill = skills.reduce((a, b) => a + b, 0) / skills.length
        return sum + avgSkill
      }, 0)

      return Math.round(totalScore / reports.length)
    }
  },

  actions: {
    // 生成评估报告
    async generateReport(sessionId: string) {
      this.loading = true
      this.error = ''

      try {
        await new Promise(resolve => setTimeout(resolve, 2000))

        const newReport: AssessmentReport = {
          id: 'report_' + Date.now(),
          sessionId,
          generatedAt: new Date().toISOString(),
          overview: {
            totalSessions: this.reports.length + 1,
            avgDuration: 285,
            attackSuccessRate: 68.5,
            defenseEfficiency: 72.3
          },
          skillAssessment: {
            penetrationTesting: Math.floor(Math.random() * 30) + 70,
            vulnerabilityAnalysis: Math.floor(Math.random() * 30) + 70,
            incidentResponse: Math.floor(Math.random() * 30) + 70,
            networkSecurity: Math.floor(Math.random() * 30) + 70,
            webSecurity: Math.floor(Math.random() * 30) + 70,
            systemHardening: Math.floor(Math.random() * 30) + 70
          },
          vulnerabilityStats: [
            { type: 'SQL注入', discovered: 12, exploited: 8, blocked: 4 },
            { type: 'XSS攻击', discovered: 8, exploited: 5, blocked: 3 },
            { type: '文件上传', discovered: 6, exploited: 4, blocked: 2 },
            { type: '权限提升', discovered: 10, exploited: 6, blocked: 4 },
            { type: '横向移动', discovered: 5, exploited: 3, blocked: 2 }
          ]
        }

        this.reports.push(newReport)
        this.currentReport = newReport

        return { success: true, report: newReport }
      } catch (error) {
        this.error = '生成报告失败'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    // 设置时间范围
    setTimeRange(range: string) {
      this.timeRange = range
    },

    // 导出报告
    async exportReport(reportId: string) {
      this.loading = true
      this.error = ''

      try {
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 这里会实现实际的导出逻辑
        console.log('导出报告:', reportId)

        return { success: true }
      } catch (error) {
        this.error = '导出报告失败'
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    // 清除错误
    clearError() {
      this.error = ''
    }
  }
})