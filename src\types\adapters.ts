// ============================================================================
// 类型适配器 - 用于在新旧类型系统之间转换
// ============================================================================

import type {
  User,
  ScenarioConfig,
  NetworkNode,
  AIAgent,
  AIAgentConfiguration,
  AIAgentStatus,
  SimulationSession,
  SimulationEvent,
  DecisionLog,
  AssessmentReport,
  UUID,
  Timestamp,
  Score,
  Percentage
} from './index'

// ============================================================================
// 旧版本接口定义（用于向后兼容）
// ============================================================================

export interface LegacyUser {
  id: string
  email: string
  name: string
  avatar?: string
  joinDate?: string
}

export interface LegacyScenarioConfig {
  id: string
  name: string
  template: string
  topology: string
  businessType: string
  vulnerabilityLevel: string
  customConfig?: string
  createdAt: string
  updatedAt: string
}

export interface LegacyNetworkNode {
  id: string
  name: string
  ip: string
  type: 'server' | 'firewall' | 'database' | 'attacker' | 'client'
  status: 'normal' | 'compromised' | 'active' | 'attacking' | 'defending'
  x: number
  y: number
  services?: string[]
  vulnerabilities?: string[]
}

export interface LegacyAIAgentConfig {
  aggressiveness: number
  intelligence: number
  stealth: number
  persistence: number
  strategy: string
}

export interface LegacyAIAgentStatus {
  status: 'active' | 'paused' | 'monitoring' | 'error'
  currentAction: string
  nextAction: string
  confidence: number
  successRate?: number
  blockRate?: number
}

export interface LegacySimulationEvent {
  id: number
  timestamp: string
  type: 'attack' | 'defense'
  subType: string
  source: string
  target: string
  status: 'success' | 'blocked' | 'pending'
  description: string
  confidence?: number
}

export interface LegacySimulationSession {
  id: string
  name: string
  scenarioId: string
  startTime: string
  endTime?: string
  status: 'preparing' | 'running' | 'paused' | 'completed'
  currentPhase: string
  duration: number
  events: LegacySimulationEvent[]
  metrics: {
    attackSuccess: number
    defenseEfficiency: number
    systemIntegrity: number
    networkTraffic: number
  }
}

export interface LegacyDecisionLog {
  id: number
  timestamp: string
  agent: 'attacker' | 'defender'
  decision: string
  reasoning: string
  confidence: number
  result: 'success' | 'blocked' | 'pending'
}

export interface LegacyAssessmentReport {
  id: string
  sessionId: string
  generatedAt: string
  overview: {
    totalSessions: number
    avgDuration: number
    attackSuccessRate: number
    defenseEfficiency: number
  }
  skillAssessment: {
    [skill: string]: number
  }
  vulnerabilityStats: Array<{
    type: string
    discovered: number
    exploited: number
    blocked: number
  }>
}

// ============================================================================
// 类型转换函数
// ============================================================================

/**
 * 将旧版用户类型转换为新版用户类型
 */
export function adaptLegacyUser(legacyUser: LegacyUser): User {
  return {
    id: legacyUser.id as UUID,
    email: legacyUser.email,
    name: legacyUser.name,
    avatar: legacyUser.avatar,
    joinDate: legacyUser.joinDate as Timestamp || new Date().toISOString(),
    role: 'user',
    preferences: {
      theme: 'dark',
      language: 'zh-CN',
      notifications: {
        email: true,
        push: true,
        simulation: true,
        reports: true
      },
      dashboard: {
        defaultView: 'overview',
        refreshInterval: 30
      }
    },
    statistics: {
      totalSessions: 0,
      completedSessions: 0,
      totalDuration: 0,
      averageScore: 0,
      rank: 0,
      achievements: [],
      skillLevels: {
        penetrationTesting: 0,
        vulnerabilityAnalysis: 0,
        incidentResponse: 0,
        networkSecurity: 0,
        webSecurity: 0,
        systemHardening: 0,
        socialEngineering: 0,
        cryptography: 0,
        forensics: 0,
        malwareAnalysis: 0
      }
    }
  }
}

/**
 * 将新版用户类型转换为旧版用户类型
 */
export function adaptUserToLegacy(user: User): LegacyUser {
  return {
    id: user.id,
    email: user.email,
    name: user.name,
    avatar: user.avatar,
    joinDate: user.joinDate
  }
}

/**
 * 将旧版场景配置转换为新版场景配置
 */
export function adaptLegacyScenarioConfig(legacyConfig: LegacyScenarioConfig): ScenarioConfig {
  return {
    id: legacyConfig.id as UUID,
    name: legacyConfig.name,
    description: `基于${legacyConfig.template}模板的场景`,
    template: {
      id: legacyConfig.template,
      name: legacyConfig.template,
      description: '',
      category: 'web_security',
      icon: '🌐',
      difficulty: 'intermediate',
      estimatedDuration: 60,
      prerequisites: [],
      tags: []
    },
    topology: {
      type: legacyConfig.topology as any,
      nodes: [],
      connections: [],
      subnets: [],
      securityZones: []
    },
    businessEnvironment: {
      industry: 'technology',
      size: 'medium',
      complianceRequirements: [],
      dataTypes: [],
      businessHours: {
        start: '09:00',
        end: '17:00',
        timezone: 'Asia/Shanghai',
        weekdays: [1, 2, 3, 4, 5]
      },
      criticalAssets: []
    },
    vulnerabilityProfile: {
      level: legacyConfig.vulnerabilityLevel as any,
      categories: [],
      customVulnerabilities: [],
      patchingPolicy: {
        criticalPatches: 1,
        highPatches: 7,
        mediumPatches: 30,
        lowPatches: 90,
        maintenanceWindow: {
          start: '02:00',
          end: '04:00',
          timezone: 'Asia/Shanghai',
          weekdays: [0]
        },
        testingRequired: true
      },
      scanningSchedule: {
        frequency: 'weekly',
        time: '02:00',
        scope: 'full',
        automated: true
      }
    },
    objectives: [],
    constraints: {
      timeLimit: 300,
      allowedTools: [],
      forbiddenActions: [],
      networkRestrictions: [],
      resourceLimits: {
        maxConcurrentConnections: 100,
        maxBandwidth: 1000,
        maxCpuUsage: 80,
        maxMemoryUsage: 80,
        maxDiskUsage: 80
      }
    },
    metadata: {
      author: 'system',
      version: '1.0.0',
      changelog: [],
      tags: [],
      difficulty: 5,
      popularity: 0,
      rating: 0,
      reviews: [],
      usage: {
        totalRuns: 0,
        successRate: 0,
        averageDuration: 0,
        averageScore: 0,
        lastUsed: new Date().toISOString()
      }
    },
    createdAt: legacyConfig.createdAt as Timestamp,
    updatedAt: legacyConfig.updatedAt as Timestamp,
    createdBy: 'system' as UUID,
    version: '1.0.0',
    status: 'active'
  }
}

/**
 * 将旧版网络节点转换为新版网络节点
 */
export function adaptLegacyNetworkNode(legacyNode: LegacyNetworkNode): NetworkNode {
  return {
    id: legacyNode.id as UUID,
    name: legacyNode.name,
    type: legacyNode.type as any,
    ip: legacyNode.ip,
    mac: undefined,
    os: {
      family: 'linux',
      name: 'Ubuntu',
      version: '20.04',
      architecture: 'x64',
      patchLevel: 'current',
      hardened: false
    },
    services: (legacyNode.services || []).map(service => ({
      id: `service_${Date.now()}_${Math.random()}` as UUID,
      name: service,
      port: 80,
      protocol: 'tcp',
      version: '1.0',
      status: 'running',
      configuration: {},
      logs: []
    })),
    vulnerabilities: (legacyNode.vulnerabilities || []).map(vuln => ({
      id: `vuln_${Date.now()}_${Math.random()}`,
      name: vuln,
      description: vuln,
      severity: 'medium',
      cvssScore: 5.0,
      exploitability: 'medium',
      impact: {
        confidentiality: 'partial',
        integrity: 'partial',
        availability: 'none'
      },
      mitigation: [],
      references: [],
      discovered: false,
      exploited: false,
      patched: false
    })),
    position: {
      x: legacyNode.x,
      y: legacyNode.y
    },
    status: legacyNode.status as any,
    metadata: {
      owner: 'system',
      department: 'IT',
      criticality: 'medium',
      dataClassification: 'internal',
      backupStatus: 'current',
      monitoringEnabled: true,
      lastSeen: new Date().toISOString()
    }
  }
}

/**
 * 生成UUID（简单实现）
 */
export function generateUUID(): UUID {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 生成当前时间戳
 */
export function generateTimestamp(): Timestamp {
  return new Date().toISOString()
}

/**
 * 验证分数范围
 */
export function validateScore(score: number): Score {
  return Math.max(0, Math.min(100, score)) as Score
}

/**
 * 验证百分比范围
 */
export function validatePercentage(percentage: number): Percentage {
  return Math.max(0, Math.min(100, percentage)) as Percentage
}

// ============================================================================
// 类型守卫函数
// ============================================================================

/**
 * 检查是否为有效的UUID
 */
export function isValidUUID(value: string): value is UUID {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(value)
}

/**
 * 检查是否为有效的时间戳
 */
export function isValidTimestamp(value: string): value is Timestamp {
  return !isNaN(Date.parse(value))
}

/**
 * 检查是否为有效的分数
 */
export function isValidScore(value: number): value is Score {
  return typeof value === 'number' && value >= 0 && value <= 100
}

/**
 * 检查是否为有效的百分比
 */
export function isValidPercentage(value: number): value is Percentage {
  return typeof value === 'number' && value >= 0 && value <= 100
}

// ============================================================================
// 默认值生成器
// ============================================================================

/**
 * 生成默认用户偏好设置
 */
export function createDefaultUserPreferences() {
  return {
    theme: 'dark' as const,
    language: 'zh-CN' as const,
    notifications: {
      email: true,
      push: true,
      simulation: true,
      reports: true
    },
    dashboard: {
      defaultView: 'overview' as const,
      refreshInterval: 30
    }
  }
}

/**
 * 生成默认用户统计信息
 */
export function createDefaultUserStatistics() {
  return {
    totalSessions: 0,
    completedSessions: 0,
    totalDuration: 0,
    averageScore: 0 as Score,
    rank: 0,
    achievements: [],
    skillLevels: {
      penetrationTesting: 0 as Score,
      vulnerabilityAnalysis: 0 as Score,
      incidentResponse: 0 as Score,
      networkSecurity: 0 as Score,
      webSecurity: 0 as Score,
      systemHardening: 0 as Score,
      socialEngineering: 0 as Score,
      cryptography: 0 as Score,
      forensics: 0 as Score,
      malwareAnalysis: 0 as Score
    }
  }
}
