// ============================================================================
// 常量定义
// ============================================================================

// ============================================================================
// 用户相关常量
// ============================================================================

export const USER_ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  OBSERVER: 'observer'
} as const

export const USER_THEMES = {
  DARK: 'dark',
  LIGHT: 'light'
} as const

export const USER_LANGUAGES = {
  ZH_CN: 'zh-CN',
  EN_US: 'en-US'
} as const

export const DASHBOARD_VIEWS = {
  OVERVIEW: 'overview',
  SIMULATION: 'simulation',
  REPORTS: 'reports'
} as const

export const ACHIEVEMENT_RARITIES = {
  COMMON: 'common',
  RARE: 'rare',
  EPIC: 'epic',
  LEGENDARY: 'legendary'
} as const

// ============================================================================
// 场景相关常量
// ============================================================================

export const SCENARIO_CATEGORIES = {
  WEB_SECURITY: 'web_security',
  NETWORK_SECURITY: 'network_security',
  CLOUD_SECURITY: 'cloud_security',
  IOT_SECURITY: 'iot_security',
  MOBILE_SECURITY: 'mobile_security'
} as const

export const SCENARIO_DIFFICULTIES = {
  BEGINNER: 'beginner',
  INTERMEDIATE: 'intermediate',
  ADVANCED: 'advanced',
  EXPERT: 'expert'
} as const

export const SCENARIO_STATUSES = {
  DRAFT: 'draft',
  ACTIVE: 'active',
  ARCHIVED: 'archived'
} as const

export const NETWORK_TOPOLOGIES = {
  SIMPLE: 'simple',
  ENTERPRISE: 'enterprise',
  CLOUD: 'cloud',
  HYBRID: 'hybrid',
  CUSTOM: 'custom'
} as const

export const NODE_TYPES = {
  SERVER: 'server',
  WORKSTATION: 'workstation',
  FIREWALL: 'firewall',
  ROUTER: 'router',
  SWITCH: 'switch',
  DATABASE: 'database',
  LOAD_BALANCER: 'load_balancer',
  PROXY: 'proxy',
  ATTACKER: 'attacker'
} as const

export const NODE_STATUSES = {
  NORMAL: 'normal',
  COMPROMISED: 'compromised',
  SUSPICIOUS: 'suspicious',
  OFFLINE: 'offline',
  MAINTENANCE: 'maintenance'
} as const

export const OS_FAMILIES = {
  WINDOWS: 'windows',
  LINUX: 'linux',
  MACOS: 'macos',
  UNIX: 'unix',
  EMBEDDED: 'embedded'
} as const

export const OS_ARCHITECTURES = {
  X86: 'x86',
  X64: 'x64',
  ARM: 'arm',
  ARM64: 'arm64'
} as const

export const VULNERABILITY_SEVERITIES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
} as const

export const VULNERABILITY_EXPLOITABILITIES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
} as const

export const VULNERABILITY_IMPACTS = {
  NONE: 'none',
  PARTIAL: 'partial',
  COMPLETE: 'complete'
} as const

// ============================================================================
// AI Agent 相关常量
// ============================================================================

export const AI_AGENT_TYPES = {
  ATTACKER: 'attacker',
  DEFENDER: 'defender'
} as const

export const AI_AGENT_STATES = {
  IDLE: 'idle',
  ACTIVE: 'active',
  PAUSED: 'paused',
  ERROR: 'error',
  LEARNING: 'learning',
  ADAPTING: 'adapting'
} as const

export const AI_MODEL_PROVIDERS = {
  OPENAI: 'openai',
  ANTHROPIC: 'anthropic',
  LOCAL: 'local',
  CUSTOM: 'custom'
} as const

export const AI_STRATEGIES = {
  AGGRESSIVE: 'aggressive',
  STEALTH: 'stealth',
  ADAPTIVE: 'adaptive',
  PERSISTENT: 'persistent',
  REACTIVE: 'reactive',
  PROACTIVE: 'proactive',
  ZERO_TRUST: 'zero_trust'
} as const

export const AI_RISK_TOLERANCES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
} as const

export const AI_TACTIC_TYPES = {
  RECONNAISSANCE: 'reconnaissance',
  EXPLOITATION: 'exploitation',
  PERSISTENCE: 'persistence',
  DEFENSE: 'defense',
  DETECTION: 'detection',
  RESPONSE: 'response'
} as const

export const AI_ACTION_TYPES = {
  // 攻击行为
  PORT_SCAN: 'port_scan',
  VULNERABILITY_SCAN: 'vulnerability_scan',
  EXPLOIT_ATTEMPT: 'exploit_attempt',
  PRIVILEGE_ESCALATION: 'privilege_escalation',
  LATERAL_MOVEMENT: 'lateral_movement',
  DATA_EXFILTRATION: 'data_exfiltration',
  PERSISTENCE_ESTABLISHMENT: 'persistence_establishment',
  CREDENTIAL_HARVESTING: 'credential_harvesting',
  SOCIAL_ENGINEERING: 'social_engineering',
  MALWARE_DEPLOYMENT: 'malware_deployment',
  // 防御行为
  THREAT_DETECTION: 'threat_detection',
  INCIDENT_RESPONSE: 'incident_response',
  SYSTEM_ISOLATION: 'system_isolation',
  PATCH_DEPLOYMENT: 'patch_deployment',
  ACCESS_REVOCATION: 'access_revocation',
  LOG_ANALYSIS: 'log_analysis',
  FORENSIC_INVESTIGATION: 'forensic_investigation',
  BACKUP_RESTORATION: 'backup_restoration'
} as const

export const AI_ERROR_TYPES = {
  CONFIGURATION: 'configuration',
  EXECUTION: 'execution',
  COMMUNICATION: 'communication',
  RESOURCE: 'resource'
} as const

export const AI_WARNING_TYPES = {
  PERFORMANCE: 'performance',
  ETHICAL: 'ethical',
  RESOURCE: 'resource',
  STRATEGY: 'strategy'
} as const

export const AI_WARNING_SEVERITIES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
} as const

// ============================================================================
// 推演会话相关常量
// ============================================================================

export const SESSION_MODES = {
  TRAINING: 'training',
  ASSESSMENT: 'assessment',
  COMPETITION: 'competition',
  RESEARCH: 'research'
} as const

export const SESSION_STATUSES = {
  CREATED: 'created',
  PREPARING: 'preparing',
  RUNNING: 'running',
  PAUSED: 'paused',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  ERROR: 'error'
} as const

export const PARTICIPANT_TYPES = {
  HUMAN: 'human',
  AI_AGENT: 'ai_agent'
} as const

export const PARTICIPANT_ROLES = {
  RED_TEAM: 'red_team',
  BLUE_TEAM: 'blue_team',
  OBSERVER: 'observer',
  FACILITATOR: 'facilitator'
} as const

export const EVENT_TYPES = {
  ATTACK: 'attack',
  DEFENSE: 'defense',
  SYSTEM: 'system',
  USER: 'user',
  AI_DECISION: 'ai_decision',
  NETWORK: 'network',
  ALERT: 'alert'
} as const

export const EVENT_SEVERITIES = {
  INFO: 'info',
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
} as const

export const EVENT_STATUSES = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  FAILED: 'failed',
  BLOCKED: 'blocked'
} as const

export const MILESTONE_TYPES = {
  OBJECTIVE_COMPLETED: 'objective_completed',
  PHASE_TRANSITION: 'phase_transition',
  CRITICAL_EVENT: 'critical_event',
  TIME_MARKER: 'time_marker'
} as const

export const MILESTONE_SIGNIFICANCES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
} as const

// ============================================================================
// 评估报告相关常量
// ============================================================================

export const REPORT_TYPES = {
  INDIVIDUAL: 'individual',
  TEAM: 'team',
  COMPARATIVE: 'comparative',
  LONGITUDINAL: 'longitudinal'
} as const

export const REPORT_STATUSES = {
  GENERATING: 'generating',
  COMPLETED: 'completed',
  FAILED: 'failed'
} as const

export const SKILL_LEVELS = {
  NOVICE: 'novice',
  BEGINNER: 'beginner',
  INTERMEDIATE: 'intermediate',
  ADVANCED: 'advanced',
  EXPERT: 'expert'
} as const

export const SKILL_CATEGORIES = {
  TECHNICAL: 'technical',
  SOCIAL: 'social',
  PHYSICAL: 'physical',
  ANALYTICAL: 'analytical'
} as const

export const SKILL_IMPORTANCES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
} as const

export const RECOMMENDATION_CATEGORIES = {
  IMMEDIATE: 'immediate',
  SHORT_TERM: 'short_term',
  LONG_TERM: 'long_term',
  STRATEGIC: 'strategic'
} as const

export const RECOMMENDATION_PRIORITIES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
} as const

export const TREND_DIRECTIONS = {
  IMPROVING: 'improving',
  DECLINING: 'declining',
  STABLE: 'stable',
  VOLATILE: 'volatile'
} as const

export const TREND_SIGNIFICANCES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
} as const

// ============================================================================
// 决策日志相关常量
// ============================================================================

export const DECISION_TYPES = {
  STRATEGIC: 'strategic',
  TACTICAL: 'tactical',
  OPERATIONAL: 'operational',
  REACTIVE: 'reactive'
} as const

export const DECISION_URGENCIES = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
} as const

export const DECISION_OUTCOMES = {
  SUCCESS: 'success',
  FAILURE: 'failure',
  PARTIAL: 'partial',
  PENDING: 'pending',
  CANCELLED: 'cancelled'
} as const

export const REASONING_SOURCES = {
  OBSERVATION: 'observation',
  INFERENCE: 'inference',
  KNOWLEDGE: 'knowledge',
  ASSUMPTION: 'assumption'
} as const

export const REASONING_IMPACTS = {
  POSITIVE: 'positive',
  NEGATIVE: 'negative',
  NEUTRAL: 'neutral'
} as const

export const FEEDBACK_SOURCES = {
  USER: 'user',
  SYSTEM: 'system',
  PEER_AGENT: 'peer_agent',
  SUPERVISOR: 'supervisor'
} as const

export const FEEDBACK_TYPES = {
  APPROVAL: 'approval',
  CRITICISM: 'criticism',
  SUGGESTION: 'suggestion',
  CORRECTION: 'correction'
} as const

// ============================================================================
// 通用常量
// ============================================================================

export const SORT_ORDERS = {
  ASC: 'asc',
  DESC: 'desc'
} as const

export const DATA_CLASSIFICATIONS = {
  PUBLIC: 'public',
  INTERNAL: 'internal',
  CONFIDENTIAL: 'confidential',
  RESTRICTED: 'restricted'
} as const

export const CRITICALITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
} as const

export const BACKUP_STATUSES = {
  CURRENT: 'current',
  OUTDATED: 'outdated',
  NONE: 'none'
} as const

export const SCANNING_FREQUENCIES = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly'
} as const

export const SCANNING_SCOPES = {
  FULL: 'full',
  INCREMENTAL: 'incremental'
} as const

// ============================================================================
// 默认值常量
// ============================================================================

export const DEFAULT_VALUES = {
  PAGINATION_SIZE: 20,
  MAX_PAGINATION_SIZE: 100,
  SESSION_TIMEOUT: 3600, // 1小时（秒）
  REFRESH_INTERVAL: 30, // 30秒
  MAX_RETRY_ATTEMPTS: 3,
  REQUEST_TIMEOUT: 30000, // 30秒（毫秒）
  DEBOUNCE_DELAY: 300, // 300毫秒
  ANIMATION_DURATION: 300, // 300毫秒
  CHART_COLORS: [
    '#00ff88', '#00a3ff', '#ff4444', '#ffa500', 
    '#8b5cf6', '#06d6a0', '#f72585', '#4cc9f0'
  ],
  SKILL_WEIGHTS: {
    penetrationTesting: 0.2,
    vulnerabilityAnalysis: 0.15,
    incidentResponse: 0.15,
    networkSecurity: 0.15,
    webSecurity: 0.15,
    systemHardening: 0.1,
    socialEngineering: 0.05,
    cryptography: 0.05
  }
} as const

// ============================================================================
// 正则表达式常量
// ============================================================================

export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  IP_ADDRESS: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  MAC_ADDRESS: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
  CIDR: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[1-2][0-9]|3[0-2])$/,
  CVE: /^CVE-\d{4}-\d{4,}$/,
  STRONG_PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
} as const

// ============================================================================
// 错误代码常量
// ============================================================================

export const ERROR_CODES = {
  // 认证错误
  AUTH_INVALID_CREDENTIALS: 'AUTH_INVALID_CREDENTIALS',
  AUTH_TOKEN_EXPIRED: 'AUTH_TOKEN_EXPIRED',
  AUTH_INSUFFICIENT_PERMISSIONS: 'AUTH_INSUFFICIENT_PERMISSIONS',
  
  // 验证错误
  VALIDATION_REQUIRED_FIELD: 'VALIDATION_REQUIRED_FIELD',
  VALIDATION_INVALID_FORMAT: 'VALIDATION_INVALID_FORMAT',
  VALIDATION_OUT_OF_RANGE: 'VALIDATION_OUT_OF_RANGE',
  
  // 资源错误
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  
  // 系统错误
  SYSTEM_INTERNAL_ERROR: 'SYSTEM_INTERNAL_ERROR',
  SYSTEM_SERVICE_UNAVAILABLE: 'SYSTEM_SERVICE_UNAVAILABLE',
  SYSTEM_TIMEOUT: 'SYSTEM_TIMEOUT',
  
  // AI错误
  AI_MODEL_ERROR: 'AI_MODEL_ERROR',
  AI_CONFIGURATION_ERROR: 'AI_CONFIGURATION_ERROR',
  AI_EXECUTION_ERROR: 'AI_EXECUTION_ERROR'
} as const
