// ============================================================================
// AI 攻防推演平台 - 类型定义
// ============================================================================

// 基础类型
export type UUID = string
export type Timestamp = string
export type Score = number // 0-100
export type Percentage = number // 0-100

// ============================================================================
// 用户相关类型
// ============================================================================

export interface User {
  id: UUID
  email: string
  name: string
  avatar?: string
  joinDate: Timestamp
  role: 'admin' | 'user' | 'observer'
  preferences: UserPreferences
  statistics: UserStatistics
}

export interface UserPreferences {
  theme: 'dark' | 'light'
  language: 'zh-CN' | 'en-US'
  notifications: {
    email: boolean
    push: boolean
    simulation: boolean
    reports: boolean
  }
  dashboard: {
    defaultView: 'overview' | 'simulation' | 'reports'
    refreshInterval: number // 秒
  }
}

export interface UserStatistics {
  totalSessions: number
  completedSessions: number
  totalDuration: number // 秒
  averageScore: Score
  rank: number
  achievements: Achievement[]
  skillLevels: SkillLevels
}

export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  unlockedAt: Timestamp
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
}

export interface SkillLevels {
  penetrationTesting: Score
  vulnerabilityAnalysis: Score
  incidentResponse: Score
  networkSecurity: Score
  webSecurity: Score
  systemHardening: Score
  socialEngineering: Score
  cryptography: Score
  forensics: Score
  malwareAnalysis: Score
}

// ============================================================================
// 场景配置相关类型
// ============================================================================

export interface ScenarioConfig {
  id: UUID
  name: string
  description: string
  template: ScenarioTemplate
  topology: NetworkTopology
  businessEnvironment: BusinessEnvironment
  vulnerabilityProfile: VulnerabilityProfile
  objectives: ScenarioObjective[]
  constraints: ScenarioConstraints
  metadata: ScenarioMetadata
  createdAt: Timestamp
  updatedAt: Timestamp
  createdBy: UUID
  version: string
  status: 'draft' | 'active' | 'archived'
}

export interface ScenarioTemplate {
  id: string
  name: string
  description: string
  category: 'web_security' | 'network_security' | 'cloud_security' | 'iot_security' | 'mobile_security'
  icon: string
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  estimatedDuration: number // 分钟
  prerequisites: string[]
  tags: string[]
}

export interface NetworkTopology {
  type: 'simple' | 'enterprise' | 'cloud' | 'hybrid' | 'custom'
  nodes: NetworkNode[]
  connections: NetworkConnection[]
  subnets: NetworkSubnet[]
  securityZones: SecurityZone[]
}

export interface NetworkNode {
  id: UUID
  name: string
  type: 'server' | 'workstation' | 'firewall' | 'router' | 'switch' | 'database' | 'load_balancer' | 'proxy' | 'attacker'
  ip: string
  mac?: string
  os: OperatingSystem
  services: Service[]
  vulnerabilities: Vulnerability[]
  position: Position2D
  status: NodeStatus
  metadata: NodeMetadata
}

export interface NetworkConnection {
  id: UUID
  sourceNodeId: UUID
  targetNodeId: UUID
  type: 'ethernet' | 'wifi' | 'vpn' | 'internet'
  protocol: 'tcp' | 'udp' | 'icmp'
  ports: number[]
  bandwidth: number // Mbps
  latency: number // ms
  encrypted: boolean
  monitored: boolean
}

export interface NetworkSubnet {
  id: UUID
  name: string
  cidr: string
  vlan?: number
  gateway: string
  dns: string[]
  dhcp: boolean
  isolated: boolean
}

export interface SecurityZone {
  id: UUID
  name: string
  type: 'dmz' | 'internal' | 'external' | 'management'
  trustLevel: 'high' | 'medium' | 'low' | 'untrusted'
  nodeIds: UUID[]
  policies: SecurityPolicy[]
}

export interface OperatingSystem {
  family: 'windows' | 'linux' | 'macos' | 'unix' | 'embedded'
  name: string
  version: string
  architecture: 'x86' | 'x64' | 'arm' | 'arm64'
  patchLevel: string
  hardened: boolean
}

export interface Service {
  id: UUID
  name: string
  port: number
  protocol: 'tcp' | 'udp'
  version: string
  status: 'running' | 'stopped' | 'disabled'
  configuration: Record<string, any>
  logs: ServiceLog[]
}

export interface ServiceLog {
  timestamp: Timestamp
  level: 'debug' | 'info' | 'warn' | 'error'
  message: string
  source: string
}

export interface Vulnerability {
  id: string
  cve?: string
  name: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  cvssScore: number
  exploitability: 'low' | 'medium' | 'high'
  impact: VulnerabilityImpact
  mitigation: string[]
  references: string[]
  discovered: boolean
  exploited: boolean
  patched: boolean
}

export interface VulnerabilityImpact {
  confidentiality: 'none' | 'partial' | 'complete'
  integrity: 'none' | 'partial' | 'complete'
  availability: 'none' | 'partial' | 'complete'
}

export interface Position2D {
  x: number
  y: number
}

export type NodeStatus = 'normal' | 'compromised' | 'suspicious' | 'offline' | 'maintenance'

export interface NodeMetadata {
  owner: string
  department: string
  criticality: 'low' | 'medium' | 'high' | 'critical'
  dataClassification: 'public' | 'internal' | 'confidential' | 'restricted'
  backupStatus: 'current' | 'outdated' | 'none'
  monitoringEnabled: boolean
  lastSeen: Timestamp
}

export interface BusinessEnvironment {
  industry: string
  size: 'small' | 'medium' | 'large' | 'enterprise'
  complianceRequirements: string[]
  dataTypes: DataType[]
  businessHours: TimeRange
  criticalAssets: CriticalAsset[]
}

export interface DataType {
  name: string
  classification: 'public' | 'internal' | 'confidential' | 'restricted'
  volume: number // GB
  location: string[]
  encrypted: boolean
  backupFrequency: 'daily' | 'weekly' | 'monthly'
}

export interface TimeRange {
  start: string // HH:MM
  end: string // HH:MM
  timezone: string
  weekdays: number[] // 0-6, 0=Sunday
}

export interface CriticalAsset {
  id: UUID
  name: string
  type: 'data' | 'system' | 'service' | 'application'
  value: number // 业务价值评分
  dependencies: UUID[]
  protectionLevel: 'basic' | 'enhanced' | 'maximum'
}

export interface VulnerabilityProfile {
  level: 'low' | 'medium' | 'high' | 'critical'
  categories: VulnerabilityCategory[]
  customVulnerabilities: Vulnerability[]
  patchingPolicy: PatchingPolicy
  scanningSchedule: ScanningSchedule
}

export interface VulnerabilityCategory {
  name: string
  enabled: boolean
  severity: 'low' | 'medium' | 'high' | 'critical'
  count: number
  examples: string[]
}

export interface PatchingPolicy {
  criticalPatches: number // 天内必须修复
  highPatches: number
  mediumPatches: number
  lowPatches: number
  maintenanceWindow: TimeRange
  testingRequired: boolean
}

export interface ScanningSchedule {
  frequency: 'daily' | 'weekly' | 'monthly'
  time: string // HH:MM
  scope: 'full' | 'incremental'
  automated: boolean
}

export interface ScenarioObjective {
  id: UUID
  type: 'primary' | 'secondary' | 'bonus'
  title: string
  description: string
  target: string
  criteria: ObjectiveCriteria
  points: number
  timeLimit?: number // 分钟
  hints: string[]
  completed: boolean
  completedAt?: Timestamp
}

export interface ObjectiveCriteria {
  type: 'access' | 'data_exfiltration' | 'privilege_escalation' | 'persistence' | 'detection_evasion'
  parameters: Record<string, any>
  validation: ValidationRule[]
}

export interface ValidationRule {
  field: string
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'exists'
  value: any
  required: boolean
}

export interface ScenarioConstraints {
  timeLimit: number // 分钟
  allowedTools: string[]
  forbiddenActions: string[]
  networkRestrictions: NetworkRestriction[]
  resourceLimits: ResourceLimits
}

export interface NetworkRestriction {
  type: 'block' | 'allow' | 'monitor'
  source: string
  destination: string
  ports: number[]
  protocols: string[]
}

export interface ResourceLimits {
  maxConcurrentConnections: number
  maxBandwidth: number // Mbps
  maxCpuUsage: Percentage
  maxMemoryUsage: Percentage
  maxDiskUsage: Percentage
}

export interface ScenarioMetadata {
  author: string
  version: string
  changelog: ChangelogEntry[]
  tags: string[]
  difficulty: number // 1-10
  popularity: number
  rating: number // 1-5
  reviews: Review[]
  usage: UsageStatistics
}

export interface ChangelogEntry {
  version: string
  date: Timestamp
  changes: string[]
  author: string
}

export interface Review {
  id: UUID
  userId: UUID
  rating: number // 1-5
  comment: string
  createdAt: Timestamp
  helpful: number
}

export interface UsageStatistics {
  totalRuns: number
  successRate: Percentage
  averageDuration: number // 分钟
  averageScore: Score
  lastUsed: Timestamp
}

export interface SecurityPolicy {
  id: UUID
  name: string
  type: 'firewall' | 'access_control' | 'data_loss_prevention' | 'intrusion_detection'
  rules: PolicyRule[]
  enabled: boolean
  priority: number
}

export interface PolicyRule {
  id: UUID
  action: 'allow' | 'deny' | 'log' | 'alert'
  source: string
  destination: string
  service: string
  condition: string
  enabled: boolean
}

// ============================================================================
// AI Agent 相关类型
// ============================================================================

export interface AIAgent {
  id: UUID
  name: string
  type: 'attacker' | 'defender'
  model: AIModel
  configuration: AIAgentConfiguration
  status: AIAgentStatus
  capabilities: AICapability[]
  performance: AIPerformanceMetrics
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface AIModel {
  name: string
  version: string
  provider: 'openai' | 'anthropic' | 'local' | 'custom'
  parameters: ModelParameters
  trainingData: TrainingDataInfo
  limitations: string[]
}

export interface ModelParameters {
  temperature: number // 0-1
  maxTokens: number
  topP: number
  frequencyPenalty: number
  presencePenalty: number
  contextWindow: number
}

export interface TrainingDataInfo {
  cutoffDate: string
  domains: string[]
  size: string
  quality: 'high' | 'medium' | 'low'
}

export interface AIAgentConfiguration {
  // 攻击者配置
  aggressiveness?: number // 0-100
  stealth?: number // 0-100
  persistence?: number // 0-100
  intelligence?: number // 0-100

  // 防御者配置
  alertness?: number // 0-100
  responseSpeed?: number // 0-100
  coverage?: number // 0-100
  adaptability?: number // 0-100

  // 通用配置
  strategy: AIStrategy
  riskTolerance: 'low' | 'medium' | 'high'
  learningEnabled: boolean
  collaborationMode: boolean
  ethicalConstraints: EthicalConstraint[]
}

export interface AIStrategy {
  name: string
  description: string
  tactics: AITactic[]
  adaptationRules: AdaptationRule[]
  fallbackStrategies: string[]
}

export interface AITactic {
  id: string
  name: string
  type: 'reconnaissance' | 'exploitation' | 'persistence' | 'defense' | 'detection' | 'response'
  priority: number
  conditions: TacticCondition[]
  actions: AIAction[]
  successCriteria: SuccessCriteria
}

export interface TacticCondition {
  field: string
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains' | 'exists'
  value: any
  weight: number
}

export interface AIAction {
  id: string
  type: ActionType
  parameters: Record<string, any>
  duration: number // 秒
  riskLevel: 'low' | 'medium' | 'high'
  prerequisites: string[]
  effects: ActionEffect[]
}

export type ActionType =
  // 攻击行为
  | 'port_scan' | 'vulnerability_scan' | 'exploit_attempt' | 'privilege_escalation'
  | 'lateral_movement' | 'data_exfiltration' | 'persistence_establishment'
  | 'credential_harvesting' | 'social_engineering' | 'malware_deployment'
  // 防御行为
  | 'threat_detection' | 'incident_response' | 'system_isolation' | 'patch_deployment'
  | 'access_revocation' | 'log_analysis' | 'forensic_investigation' | 'backup_restoration'

export interface ActionEffect {
  target: string
  type: 'status_change' | 'data_modification' | 'access_gained' | 'alert_triggered'
  value: any
  probability: Percentage
  duration: number // 秒，-1表示永久
}

export interface SuccessCriteria {
  metrics: string[]
  thresholds: Record<string, number>
  timeLimit: number // 秒
}

export interface AdaptationRule {
  trigger: string
  condition: string
  action: 'increase_aggressiveness' | 'change_strategy' | 'add_tactic' | 'retreat'
  parameters: Record<string, any>
}

export interface EthicalConstraint {
  type: 'data_protection' | 'system_availability' | 'user_privacy' | 'legal_compliance'
  description: string
  severity: 'warning' | 'error' | 'critical'
  enforced: boolean
}

export interface AIAgentStatus {
  state: 'idle' | 'active' | 'paused' | 'error' | 'learning' | 'adapting'
  currentAction?: AIAction
  nextPlannedAction?: AIAction
  confidence: Percentage
  lastUpdate: Timestamp
  errors: AIError[]
  warnings: AIWarning[]
}

export interface AIError {
  id: UUID
  type: 'configuration' | 'execution' | 'communication' | 'resource'
  message: string
  details: string
  timestamp: Timestamp
  resolved: boolean
}

export interface AIWarning {
  id: UUID
  type: 'performance' | 'ethical' | 'resource' | 'strategy'
  message: string
  severity: 'low' | 'medium' | 'high'
  timestamp: Timestamp
  acknowledged: boolean
}

export interface AICapability {
  name: string
  category: 'technical' | 'social' | 'physical' | 'analytical'
  level: 'basic' | 'intermediate' | 'advanced' | 'expert'
  description: string
  prerequisites: string[]
  tools: string[]
}

export interface AIPerformanceMetrics {
  successRate: Percentage
  averageResponseTime: number // 毫秒
  accuracyRate: Percentage
  adaptationSpeed: number // 学习新策略的速度
  resourceEfficiency: Percentage
  collaborationScore: Score
  ethicalCompliance: Percentage
  userSatisfaction: Score
  trends: PerformanceTrend[]
}

export interface PerformanceTrend {
  metric: string
  values: number[]
  timestamps: Timestamp[]
  trend: 'improving' | 'declining' | 'stable'
}

// ============================================================================
// 推演会话相关类型
// ============================================================================

export interface SimulationSession {
  id: UUID
  name: string
  description: string
  scenarioId: UUID
  participants: SessionParticipant[]
  configuration: SessionConfiguration
  timeline: SimulationTimeline
  status: SessionStatus
  results: SessionResults
  createdAt: Timestamp
  startedAt?: Timestamp
  endedAt?: Timestamp
  createdBy: UUID
}

export interface SessionParticipant {
  id: UUID
  type: 'human' | 'ai_agent'
  role: 'red_team' | 'blue_team' | 'observer' | 'facilitator'
  name: string
  permissions: ParticipantPermission[]
  joinedAt: Timestamp
  active: boolean
}

export interface ParticipantPermission {
  action: string
  scope: string[]
  granted: boolean
  grantedBy: UUID
  grantedAt: Timestamp
}

export interface SessionConfiguration {
  mode: 'training' | 'assessment' | 'competition' | 'research'
  duration: number // 分钟
  realTimeMode: boolean
  pauseAllowed: boolean
  hintSystem: HintSystemConfig
  scoring: ScoringConfig
  logging: LoggingConfig
  notifications: NotificationConfig
}

export interface HintSystemConfig {
  enabled: boolean
  maxHints: number
  hintPenalty: number // 扣分
  adaptiveHints: boolean
  hintTypes: string[]
}

export interface ScoringConfig {
  algorithm: 'time_based' | 'objective_based' | 'hybrid'
  baseScore: number
  timePenalty: number
  bonusMultiplier: number
  penaltyRules: PenaltyRule[]
}

export interface PenaltyRule {
  trigger: string
  penalty: number
  description: string
}

export interface LoggingConfig {
  level: 'minimal' | 'standard' | 'detailed' | 'verbose'
  includeAIDecisions: boolean
  includeNetworkTraffic: boolean
  includeSystemLogs: boolean
  retentionPeriod: number // 天
}

export interface NotificationConfig {
  realTimeAlerts: boolean
  emailSummary: boolean
  webhookUrl?: string
  slackIntegration?: SlackConfig
}

export interface SlackConfig {
  webhookUrl: string
  channel: string
  mentionUsers: string[]
}

export type SessionStatus = 'created' | 'preparing' | 'running' | 'paused' | 'completed' | 'cancelled' | 'error'

export interface SimulationTimeline {
  phases: SimulationPhase[]
  events: SimulationEvent[]
  milestones: Milestone[]
  currentPhase?: string
  progress: Percentage
}

export interface SimulationPhase {
  id: string
  name: string
  description: string
  startTime: number // 相对开始时间（秒）
  duration: number // 持续时间（秒）
  objectives: string[]
  completed: boolean
  completedAt?: Timestamp
}

export interface SimulationEvent {
  id: UUID
  type: EventType
  subType: string
  timestamp: Timestamp
  source: EventSource
  target?: EventTarget
  description: string
  details: EventDetails
  severity: 'info' | 'low' | 'medium' | 'high' | 'critical'
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'blocked'
  relatedEvents: UUID[]
  tags: string[]
}

export type EventType = 'attack' | 'defense' | 'system' | 'user' | 'ai_decision' | 'network' | 'alert'

export interface EventSource {
  id: UUID
  type: 'user' | 'ai_agent' | 'system' | 'network_node'
  name: string
  ip?: string
}

export interface EventTarget {
  id: UUID
  type: 'network_node' | 'service' | 'data' | 'user' | 'system'
  name: string
  ip?: string
}

export interface EventDetails {
  method?: string
  tool?: string
  payload?: string
  response?: string
  confidence?: Percentage
  impact?: ImpactAssessment
  mitigation?: string[]
  evidence?: Evidence[]
}

export interface ImpactAssessment {
  confidentiality: 'none' | 'low' | 'medium' | 'high'
  integrity: 'none' | 'low' | 'medium' | 'high'
  availability: 'none' | 'low' | 'medium' | 'high'
  businessImpact: Score
  technicalImpact: Score
}

export interface Evidence {
  type: 'log' | 'file' | 'network_capture' | 'screenshot' | 'memory_dump'
  location: string
  hash: string
  size: number
  timestamp: Timestamp
  metadata: Record<string, any>
}

export interface Milestone {
  id: UUID
  name: string
  description: string
  type: 'objective_completed' | 'phase_transition' | 'critical_event' | 'time_marker'
  timestamp: Timestamp
  achievedBy?: UUID
  points?: number
  significance: 'low' | 'medium' | 'high'
}

export interface SessionResults {
  finalScore: Score
  objectives: ObjectiveResult[]
  performance: SessionPerformance
  timeline: ResultTimeline
  insights: SessionInsight[]
  recommendations: Recommendation[]
  artifacts: SessionArtifact[]
}

export interface ObjectiveResult {
  objectiveId: UUID
  completed: boolean
  score: Score
  timeToComplete?: number // 秒
  attempts: number
  hintsUsed: number
  method: string
  evidence: Evidence[]
}

export interface SessionPerformance {
  overallRating: Score
  categories: PerformanceCategory[]
  strengths: string[]
  weaknesses: string[]
  improvement: ImprovementArea[]
}

export interface PerformanceCategory {
  name: string
  score: Score
  weight: number
  details: string
  benchmark: Score
}

export interface ImprovementArea {
  category: string
  priority: 'low' | 'medium' | 'high'
  description: string
  resources: LearningResource[]
}

export interface LearningResource {
  type: 'article' | 'video' | 'course' | 'tool' | 'practice'
  title: string
  url: string
  provider: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedTime: number // 分钟
}

export interface ResultTimeline {
  duration: number // 秒
  phases: PhaseResult[]
  criticalEvents: CriticalEvent[]
  efficiency: EfficiencyMetrics
}

export interface PhaseResult {
  phaseId: string
  duration: number // 秒
  score: Score
  events: number
  success: boolean
}

export interface CriticalEvent {
  timestamp: Timestamp
  type: string
  description: string
  impact: Score
  response: string
}

export interface EfficiencyMetrics {
  timeToFirstSuccess: number // 秒
  averageResponseTime: number // 秒
  resourceUtilization: Percentage
  errorRate: Percentage
}

export interface SessionInsight {
  type: 'pattern' | 'anomaly' | 'trend' | 'correlation'
  title: string
  description: string
  confidence: Percentage
  evidence: string[]
  actionable: boolean
}

export interface Recommendation {
  category: 'security' | 'process' | 'training' | 'technology'
  priority: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  implementation: ImplementationGuide
  expectedImpact: Score
  effort: 'low' | 'medium' | 'high'
}

export interface ImplementationGuide {
  steps: string[]
  timeline: string
  resources: string[]
  risks: string[]
  successMetrics: string[]
}

export interface SessionArtifact {
  id: UUID
  type: 'report' | 'log' | 'capture' | 'screenshot' | 'recording'
  name: string
  description: string
  size: number
  format: string
  location: string
  createdAt: Timestamp
  downloadable: boolean
  shareable: boolean
}

// ============================================================================
// 评估报告相关类型
// ============================================================================

export interface AssessmentReport {
  id: UUID
  sessionId: UUID
  type: 'individual' | 'team' | 'comparative' | 'longitudinal'
  title: string
  description: string
  generatedAt: Timestamp
  generatedBy: UUID
  status: 'generating' | 'completed' | 'failed'
  overview: ReportOverview
  skillAssessment: SkillAssessment
  performanceAnalysis: PerformanceAnalysis
  vulnerabilityAnalysis: VulnerabilityAnalysis
  recommendations: ReportRecommendation[]
  benchmarks: BenchmarkComparison[]
  trends: TrendAnalysis[]
  metadata: ReportMetadata
}

export interface ReportOverview {
  totalSessions: number
  timeRange: TimeRange
  avgDuration: number // 分钟
  avgScore: Score
  successRate: Percentage
  improvementRate: Percentage
  participantCount: number
  scenarioTypes: string[]
  keyMetrics: KeyMetric[]
}

export interface KeyMetric {
  name: string
  value: number
  unit: string
  trend: 'up' | 'down' | 'stable'
  change: number // 百分比变化
  significance: 'low' | 'medium' | 'high'
}

export interface SkillAssessment {
  overall: SkillLevel
  categories: SkillCategory[]
  strengths: SkillStrength[]
  gaps: SkillGap[]
  progression: SkillProgression[]
  certificationReadiness: CertificationReadiness[]
}

export interface SkillLevel {
  score: Score
  level: 'novice' | 'beginner' | 'intermediate' | 'advanced' | 'expert'
  confidence: Percentage
  reliability: Percentage
  lastAssessed: Timestamp
}

export interface SkillCategory {
  name: string
  score: Score
  weight: number
  subSkills: SubSkill[]
  benchmark: Score
  industryAverage: Score
  improvement: number // 相比上次的改进
}

export interface SubSkill {
  name: string
  score: Score
  importance: 'low' | 'medium' | 'high' | 'critical'
  evidence: SkillEvidence[]
  lastDemonstrated: Timestamp
}

export interface SkillEvidence {
  type: 'task_completion' | 'tool_usage' | 'decision_quality' | 'time_efficiency'
  description: string
  score: Score
  timestamp: Timestamp
  context: string
}

export interface SkillStrength {
  skill: string
  score: Score
  description: string
  examples: string[]
  marketValue: 'low' | 'medium' | 'high'
}

export interface SkillGap {
  skill: string
  currentLevel: Score
  targetLevel: Score
  priority: 'low' | 'medium' | 'high' | 'critical'
  impact: string
  learningPath: LearningPath
}

export interface LearningPath {
  estimatedTime: number // 小时
  difficulty: 'easy' | 'medium' | 'hard'
  prerequisites: string[]
  milestones: LearningMilestone[]
  resources: LearningResource[]
}

export interface LearningMilestone {
  name: string
  description: string
  estimatedTime: number // 小时
  skills: string[]
  assessment: string
}

export interface SkillProgression {
  skill: string
  timeline: ProgressionPoint[]
  trend: 'improving' | 'declining' | 'stable' | 'volatile'
  velocity: number // 技能提升速度
  projection: ProgressionProjection
}

export interface ProgressionPoint {
  timestamp: Timestamp
  score: Score
  context: string
  milestone?: string
}

export interface ProgressionProjection {
  timeToNextLevel: number // 天
  confidence: Percentage
  factors: ProjectionFactor[]
}

export interface ProjectionFactor {
  name: string
  impact: 'positive' | 'negative' | 'neutral'
  weight: number
  description: string
}

export interface CertificationReadiness {
  certification: string
  readiness: Percentage
  estimatedTime: number // 小时
  requiredSkills: RequiredSkill[]
  recommendedPath: string[]
}

export interface RequiredSkill {
  name: string
  required: Score
  current: Score
  gap: number
  priority: number
}

export interface PerformanceAnalysis {
  efficiency: EfficiencyAnalysis
  accuracy: AccuracyAnalysis
  adaptability: AdaptabilityAnalysis
  collaboration: CollaborationAnalysis
  decisionMaking: DecisionMakingAnalysis
  stressResponse: StressResponseAnalysis
}

export interface EfficiencyAnalysis {
  overallScore: Score
  timeManagement: Score
  resourceUtilization: Score
  taskPrioritization: Score
  automationUsage: Score
  bottlenecks: Bottleneck[]
  improvements: EfficiencyImprovement[]
}

export interface Bottleneck {
  area: string
  impact: Score
  frequency: number
  avgDelay: number // 秒
  suggestions: string[]
}

export interface EfficiencyImprovement {
  area: string
  potential: Score
  effort: 'low' | 'medium' | 'high'
  timeline: string
  roi: number
}

export interface AccuracyAnalysis {
  overallScore: Score
  falsePositives: number
  falseNegatives: number
  precision: Percentage
  recall: Percentage
  f1Score: number
  errorPatterns: ErrorPattern[]
}

export interface ErrorPattern {
  type: string
  frequency: number
  context: string[]
  impact: Score
  prevention: string[]
}

export interface AdaptabilityAnalysis {
  overallScore: Score
  learningSpeed: Score
  strategyAdjustment: Score
  toolAdoption: Score
  environmentAdaptation: Score
  examples: AdaptabilityExample[]
}

export interface AdaptabilityExample {
  scenario: string
  challenge: string
  response: string
  outcome: Score
  lessons: string[]
}

export interface CollaborationAnalysis {
  overallScore: Score
  communication: Score
  teamwork: Score
  leadership: Score
  knowledgeSharing: Score
  conflictResolution: Score
  interactions: CollaborationInteraction[]
}

export interface CollaborationInteraction {
  type: 'communication' | 'coordination' | 'support' | 'conflict'
  participants: string[]
  outcome: Score
  duration: number // 秒
  effectiveness: Score
}

export interface DecisionMakingAnalysis {
  overallScore: Score
  speed: Score
  quality: Score
  consistency: Score
  riskAssessment: Score
  informationUsage: Score
  decisions: DecisionAnalysis[]
}

export interface DecisionAnalysis {
  context: string
  options: string[]
  chosen: string
  reasoning: string
  outcome: Score
  timeToDecide: number // 秒
  confidence: Percentage
  factors: DecisionFactor[]
}

export interface DecisionFactor {
  name: string
  weight: number
  value: any
  influence: 'positive' | 'negative' | 'neutral'
}

export interface StressResponseAnalysis {
  overallScore: Score
  pressureHandling: Score
  performanceUnderStress: Score
  recoveryTime: number // 秒
  stressIndicators: StressIndicator[]
  copingStrategies: CopingStrategy[]
}

export interface StressIndicator {
  type: 'performance_drop' | 'error_increase' | 'time_pressure' | 'complexity'
  threshold: number
  observed: number
  impact: Score
}

export interface CopingStrategy {
  name: string
  effectiveness: Score
  usage: number
  context: string[]
}

export interface VulnerabilityAnalysis {
  discovered: VulnerabilityStats
  exploited: VulnerabilityStats
  mitigated: VulnerabilityStats
  trends: VulnerabilityTrend[]
  patterns: VulnerabilityPattern[]
  coverage: CoverageAnalysis
}

export interface VulnerabilityStats {
  total: number
  bySeverity: Record<string, number>
  byCategory: Record<string, number>
  bySource: Record<string, number>
  timeline: VulnerabilityTimelinePoint[]
}

export interface VulnerabilityTimelinePoint {
  timestamp: Timestamp
  count: number
  severity: string
  category: string
}

export interface VulnerabilityTrend {
  category: string
  direction: 'increasing' | 'decreasing' | 'stable'
  rate: number // 每天变化数量
  significance: 'low' | 'medium' | 'high'
  factors: string[]
}

export interface VulnerabilityPattern {
  name: string
  description: string
  frequency: number
  impact: Score
  indicators: string[]
  mitigation: string[]
}

export interface CoverageAnalysis {
  overall: Percentage
  byAssetType: Record<string, Percentage>
  byNetwork: Record<string, Percentage>
  gaps: CoverageGap[]
  recommendations: string[]
}

export interface CoverageGap {
  area: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  impact: string
  effort: 'low' | 'medium' | 'high'
  priority: number
}

export interface ReportRecommendation {
  id: UUID
  category: 'immediate' | 'short_term' | 'long_term' | 'strategic'
  priority: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  rationale: string
  implementation: ImplementationPlan
  impact: ImpactEstimate
  dependencies: string[]
  risks: Risk[]
  success_metrics: string[]
}

export interface ImplementationPlan {
  phases: ImplementationPhase[]
  timeline: string
  resources: ResourceRequirement[]
  milestones: string[]
  rollback: string
}

export interface ImplementationPhase {
  name: string
  description: string
  duration: string
  tasks: string[]
  deliverables: string[]
  risks: string[]
}

export interface ResourceRequirement {
  type: 'human' | 'technology' | 'financial' | 'time'
  description: string
  quantity: number
  unit: string
  cost?: number
}

export interface ImpactEstimate {
  security: Score
  efficiency: Score
  cost: number
  timeline: string
  confidence: Percentage
  assumptions: string[]
}

export interface Risk {
  name: string
  probability: Percentage
  impact: Score
  mitigation: string[]
  contingency: string
}

export interface BenchmarkComparison {
  category: string
  userScore: Score
  benchmarks: Benchmark[]
  position: BenchmarkPosition
  insights: string[]
}

export interface Benchmark {
  name: string
  score: Score
  description: string
  source: string
  sampleSize: number
}

export interface BenchmarkPosition {
  percentile: number
  rank: number
  total: number
  category: string
}

export interface TrendAnalysis {
  metric: string
  timeframe: string
  trend: 'improving' | 'declining' | 'stable' | 'volatile'
  rate: number
  significance: 'low' | 'medium' | 'high'
  forecast: TrendForecast
  factors: TrendFactor[]
}

export interface TrendForecast {
  shortTerm: ForecastPoint[]
  longTerm: ForecastPoint[]
  confidence: Percentage
  assumptions: string[]
}

export interface ForecastPoint {
  timestamp: Timestamp
  value: number
  confidence: Percentage
}

export interface TrendFactor {
  name: string
  correlation: number
  impact: 'positive' | 'negative' | 'neutral'
  description: string
}

export interface ReportMetadata {
  version: string
  template: string
  generationTime: number // 秒
  dataQuality: DataQuality
  limitations: string[]
  methodology: string[]
  references: string[]
  exportFormats: string[]
}

export interface DataQuality {
  completeness: Percentage
  accuracy: Percentage
  consistency: Percentage
  timeliness: Percentage
  issues: DataIssue[]
}

export interface DataIssue {
  type: 'missing' | 'inconsistent' | 'outdated' | 'invalid'
  description: string
  impact: 'low' | 'medium' | 'high'
  affected: string[]
}

// ============================================================================
// 决策日志相关类型
// ============================================================================

export interface DecisionLog {
  id: UUID
  sessionId: UUID
  agentId: UUID
  timestamp: Timestamp
  type: 'strategic' | 'tactical' | 'operational' | 'reactive'
  decision: Decision
  context: DecisionContext
  reasoning: DecisionReasoning
  outcome: DecisionOutcome
  confidence: Percentage
  alternatives: Alternative[]
  feedback: DecisionFeedback[]
}

export interface Decision {
  action: ActionType
  target: string
  parameters: Record<string, any>
  priority: number
  urgency: 'low' | 'medium' | 'high' | 'critical'
  reversible: boolean
  cost: number
  expectedBenefit: number
}

export interface DecisionContext {
  phase: string
  objectives: string[]
  constraints: string[]
  resources: ResourceStatus[]
  threats: ThreatStatus[]
  opportunities: OpportunityStatus[]
  timeRemaining: number // 秒
}

export interface ResourceStatus {
  type: string
  available: number
  required: number
  utilization: Percentage
}

export interface ThreatStatus {
  id: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  probability: Percentage
  impact: Score
  active: boolean
}

export interface OpportunityStatus {
  id: string
  value: Score
  probability: Percentage
  timeWindow: number // 秒
  requirements: string[]
}

export interface DecisionReasoning {
  factors: ReasoningFactor[]
  weights: Record<string, number>
  logic: string
  assumptions: string[]
  uncertainties: string[]
  biases: string[]
}

export interface ReasoningFactor {
  name: string
  value: any
  weight: number
  source: 'observation' | 'inference' | 'knowledge' | 'assumption'
  confidence: Percentage
  impact: 'positive' | 'negative' | 'neutral'
}

export interface DecisionOutcome {
  status: 'success' | 'failure' | 'partial' | 'pending' | 'cancelled'
  actualBenefit: number
  actualCost: number
  sideEffects: SideEffect[]
  lessons: string[]
  duration: number // 秒
  metrics: OutcomeMetric[]
}

export interface SideEffect {
  type: 'positive' | 'negative' | 'neutral'
  description: string
  impact: Score
  unexpected: boolean
}

export interface OutcomeMetric {
  name: string
  expected: number
  actual: number
  variance: number
  unit: string
}

export interface Alternative {
  action: ActionType
  description: string
  pros: string[]
  cons: string[]
  estimatedOutcome: Score
  confidence: Percentage
  rejected: boolean
  rejectionReason: string
}

export interface DecisionFeedback {
  source: 'user' | 'system' | 'peer_agent' | 'supervisor'
  type: 'approval' | 'criticism' | 'suggestion' | 'correction'
  content: string
  timestamp: Timestamp
  incorporated: boolean
}

// ============================================================================
// 通用工具类型
// ============================================================================

export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: APIError
  metadata?: ResponseMetadata
}

export interface APIError {
  code: string
  message: string
  details?: Record<string, any>
  timestamp: Timestamp
  requestId: string
}

export interface ResponseMetadata {
  requestId: string
  timestamp: Timestamp
  processingTime: number // 毫秒
  version: string
  rateLimit?: RateLimit
}

export interface RateLimit {
  limit: number
  remaining: number
  reset: Timestamp
}

export interface PaginatedResponse<T> {
  items: T[]
  pagination: Pagination
}

export interface Pagination {
  page: number
  pageSize: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrevious: boolean
}

export interface FilterOptions {
  search?: string
  category?: string
  status?: string
  dateRange?: DateRange
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface DateRange {
  start: Timestamp
  end: Timestamp
}

// 导出所有类型的联合类型，便于类型检查
export type AllTypes =
  | User | ScenarioConfig | NetworkNode | AIAgent | SimulationSession
  | AssessmentReport | DecisionLog | SimulationEvent | AIAction

// ============================================================================
// 重新导出工具函数和常量
// ============================================================================

// 从适配器模块导出
export type {
  LegacyUser,
  LegacyScenarioConfig,
  LegacyNetworkNode,
  LegacyAIAgentConfig,
  LegacyAIAgentStatus,
  LegacySimulationEvent,
  LegacySimulationSession,
  LegacyDecisionLog,
  LegacyAssessmentReport
} from './adapters'

export {
  adaptLegacyUser,
  adaptUserToLegacy,
  adaptLegacyScenarioConfig,
  adaptLegacyNetworkNode,
  generateUUID as adapterGenerateUUID,
  generateTimestamp as adapterGenerateTimestamp,
  validateScore,
  validatePercentage,
  isValidUUID,
  isValidTimestamp,
  isValidScore,
  isValidPercentage,
  createDefaultUserPreferences,
  createDefaultUserStatistics
} from './adapters'

// 从工具模块导出
export {
  validateUser,
  validateScenarioConfig,
  validateNetworkNode,
  validateSimulationSession,
  scoreToLevel,
  levelToScoreRange,
  timestampToRelative,
  secondsToReadable,
  bytesToReadable,
  generateUUID,
  generateTimestamp,
  generateRandomScore,
  generateRandomPercentage,
  sortByTimestamp,
  sortByScore,
  filterActiveSessions,
  filterCompletedSessions,
  filterByDateRange,
  calculateAverageScore,
  calculateSuccessRate,
  calculateSkillDistribution,
  calculateTrend,
  formatScore,
  formatPercentage,
  formatTimestamp,
  formatUserName,
  safeParseJSON,
  safeGet,
  sanitizeScore,
  sanitizePercentage
} from './utils'

// 从常量模块导出
export {
  USER_ROLES,
  USER_THEMES,
  USER_LANGUAGES,
  DASHBOARD_VIEWS,
  ACHIEVEMENT_RARITIES,
  SCENARIO_CATEGORIES,
  SCENARIO_DIFFICULTIES,
  SCENARIO_STATUSES,
  NETWORK_TOPOLOGIES,
  NODE_TYPES,
  NODE_STATUSES,
  OS_FAMILIES,
  OS_ARCHITECTURES,
  VULNERABILITY_SEVERITIES,
  VULNERABILITY_EXPLOITABILITIES,
  VULNERABILITY_IMPACTS,
  AI_AGENT_TYPES,
  AI_AGENT_STATES,
  AI_MODEL_PROVIDERS,
  AI_STRATEGIES,
  AI_RISK_TOLERANCES,
  AI_TACTIC_TYPES,
  AI_ACTION_TYPES,
  AI_ERROR_TYPES,
  AI_WARNING_TYPES,
  AI_WARNING_SEVERITIES,
  SESSION_MODES,
  SESSION_STATUSES,
  PARTICIPANT_TYPES,
  PARTICIPANT_ROLES,
  EVENT_TYPES,
  EVENT_SEVERITIES,
  EVENT_STATUSES,
  MILESTONE_TYPES,
  MILESTONE_SIGNIFICANCES,
  REPORT_TYPES,
  REPORT_STATUSES,
  SKILL_LEVELS,
  SKILL_CATEGORIES,
  SKILL_IMPORTANCES,
  RECOMMENDATION_CATEGORIES,
  RECOMMENDATION_PRIORITIES,
  TREND_DIRECTIONS,
  TREND_SIGNIFICANCES,
  DECISION_TYPES,
  DECISION_URGENCIES,
  DECISION_OUTCOMES,
  REASONING_SOURCES,
  REASONING_IMPACTS,
  FEEDBACK_SOURCES,
  FEEDBACK_TYPES,
  SORT_ORDERS,
  DATA_CLASSIFICATIONS,
  CRITICALITY_LEVELS,
  BACKUP_STATUSES,
  SCANNING_FREQUENCIES,
  SCANNING_SCOPES,
  DEFAULT_VALUES,
  REGEX_PATTERNS,
  ERROR_CODES
} from './constants'
