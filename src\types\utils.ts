// ============================================================================
// 类型工具函数
// ============================================================================

import type {
  UUID,
  Timestamp,
  Score,
  Percentage,
  User,
  ScenarioConfig,
  NetworkNode,
  SimulationSession,
  AssessmentReport,
  DecisionLog,
  AIAgent,
  SimulationEvent
} from './index'

// ============================================================================
// 数据验证函数
// ============================================================================

/**
 * 验证用户数据完整性
 */
export function validateUser(user: Partial<User>): user is User {
  return !!(
    user.id &&
    user.email &&
    user.name &&
    user.joinDate &&
    user.role &&
    user.preferences &&
    user.statistics
  )
}

/**
 * 验证场景配置完整性
 */
export function validateScenarioConfig(config: Partial<ScenarioConfig>): config is ScenarioConfig {
  return !!(
    config.id &&
    config.name &&
    config.template &&
    config.topology &&
    config.businessEnvironment &&
    config.vulnerabilityProfile &&
    config.createdAt &&
    config.updatedAt
  )
}

/**
 * 验证网络节点完整性
 */
export function validateNetworkNode(node: Partial<NetworkNode>): node is NetworkNode {
  return !!(
    node.id &&
    node.name &&
    node.type &&
    node.ip &&
    node.os &&
    node.position &&
    node.status &&
    node.metadata
  )
}

/**
 * 验证推演会话完整性
 */
export function validateSimulationSession(session: Partial<SimulationSession>): session is SimulationSession {
  return !!(
    session.id &&
    session.name &&
    session.scenarioId &&
    session.participants &&
    session.configuration &&
    session.timeline &&
    session.status &&
    session.createdAt
  )
}

// ============================================================================
// 数据转换函数
// ============================================================================

/**
 * 将分数转换为等级
 */
export function scoreToLevel(score: Score): 'novice' | 'beginner' | 'intermediate' | 'advanced' | 'expert' {
  if (score >= 90) return 'expert'
  if (score >= 75) return 'advanced'
  if (score >= 60) return 'intermediate'
  if (score >= 40) return 'beginner'
  return 'novice'
}

/**
 * 将等级转换为分数范围
 */
export function levelToScoreRange(level: string): { min: Score; max: Score } {
  switch (level) {
    case 'expert': return { min: 90 as Score, max: 100 as Score }
    case 'advanced': return { min: 75 as Score, max: 89 as Score }
    case 'intermediate': return { min: 60 as Score, max: 74 as Score }
    case 'beginner': return { min: 40 as Score, max: 59 as Score }
    case 'novice': return { min: 0 as Score, max: 39 as Score }
    default: return { min: 0 as Score, max: 100 as Score }
  }
}

/**
 * 将时间戳转换为相对时间描述
 */
export function timestampToRelative(timestamp: Timestamp): string {
  const now = new Date()
  const date = new Date(timestamp)
  const diffMs = now.getTime() - date.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMinutes < 1) return '刚刚'
  if (diffMinutes < 60) return `${diffMinutes}分钟前`
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays < 7) return `${diffDays}天前`
  if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`
  if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月前`
  return `${Math.floor(diffDays / 365)}年前`
}

/**
 * 将秒数转换为可读的时间格式
 */
export function secondsToReadable(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

/**
 * 将字节数转换为可读的大小格式
 */
export function bytesToReadable(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`
}

// ============================================================================
// 数据生成函数
// ============================================================================

/**
 * 生成随机UUID
 */
export function generateUUID(): UUID {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  }) as UUID
}

/**
 * 生成当前时间戳
 */
export function generateTimestamp(): Timestamp {
  return new Date().toISOString() as Timestamp
}

/**
 * 生成随机分数
 */
export function generateRandomScore(min: number = 0, max: number = 100): Score {
  return Math.floor(Math.random() * (max - min + 1) + min) as Score
}

/**
 * 生成随机百分比
 */
export function generateRandomPercentage(): Percentage {
  return Math.floor(Math.random() * 101) as Percentage
}

// ============================================================================
// 数据过滤和排序函数
// ============================================================================

/**
 * 按时间戳排序
 */
export function sortByTimestamp<T extends { timestamp?: Timestamp; createdAt?: Timestamp }>(
  items: T[],
  order: 'asc' | 'desc' = 'desc'
): T[] {
  return items.sort((a, b) => {
    const timeA = new Date(a.timestamp || a.createdAt || 0).getTime()
    const timeB = new Date(b.timestamp || b.createdAt || 0).getTime()
    return order === 'desc' ? timeB - timeA : timeA - timeB
  })
}

/**
 * 按分数排序
 */
export function sortByScore<T extends { score?: Score; averageScore?: Score }>(
  items: T[],
  order: 'asc' | 'desc' = 'desc'
): T[] {
  return items.sort((a, b) => {
    const scoreA = a.score || a.averageScore || 0
    const scoreB = b.score || b.averageScore || 0
    return order === 'desc' ? scoreB - scoreA : scoreA - scoreB
  })
}

/**
 * 过滤活跃会话
 */
export function filterActiveSessions(sessions: SimulationSession[]): SimulationSession[] {
  return sessions.filter(session => 
    session.status === 'running' || session.status === 'paused'
  )
}

/**
 * 过滤已完成会话
 */
export function filterCompletedSessions(sessions: SimulationSession[]): SimulationSession[] {
  return sessions.filter(session => session.status === 'completed')
}

/**
 * 按日期范围过滤
 */
export function filterByDateRange<T extends { timestamp?: Timestamp; createdAt?: Timestamp }>(
  items: T[],
  startDate: Timestamp,
  endDate: Timestamp
): T[] {
  const start = new Date(startDate).getTime()
  const end = new Date(endDate).getTime()
  
  return items.filter(item => {
    const itemTime = new Date(item.timestamp || item.createdAt || 0).getTime()
    return itemTime >= start && itemTime <= end
  })
}

// ============================================================================
// 统计计算函数
// ============================================================================

/**
 * 计算平均分数
 */
export function calculateAverageScore(scores: Score[]): Score {
  if (scores.length === 0) return 0 as Score
  const sum = scores.reduce((acc, score) => acc + score, 0)
  return Math.round(sum / scores.length) as Score
}

/**
 * 计算成功率
 */
export function calculateSuccessRate(total: number, successful: number): Percentage {
  if (total === 0) return 0 as Percentage
  return Math.round((successful / total) * 100) as Percentage
}

/**
 * 计算技能分布
 */
export function calculateSkillDistribution(users: User[]): Record<string, Score> {
  const skillTotals: Record<string, number[]> = {}
  
  users.forEach(user => {
    Object.entries(user.statistics.skillLevels).forEach(([skill, score]) => {
      if (!skillTotals[skill]) {
        skillTotals[skill] = []
      }
      skillTotals[skill].push(score)
    })
  })
  
  const distribution: Record<string, Score> = {}
  Object.entries(skillTotals).forEach(([skill, scores]) => {
    distribution[skill] = calculateAverageScore(scores as Score[])
  })
  
  return distribution
}

/**
 * 计算趋势
 */
export function calculateTrend(values: number[]): 'improving' | 'declining' | 'stable' {
  if (values.length < 2) return 'stable'
  
  const firstHalf = values.slice(0, Math.floor(values.length / 2))
  const secondHalf = values.slice(Math.floor(values.length / 2))
  
  const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length
  const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length
  
  const difference = secondAvg - firstAvg
  const threshold = Math.max(firstAvg * 0.05, 1) // 5% 或至少1个单位的变化
  
  if (difference > threshold) return 'improving'
  if (difference < -threshold) return 'declining'
  return 'stable'
}

// ============================================================================
// 数据格式化函数
// ============================================================================

/**
 * 格式化分数显示
 */
export function formatScore(score: Score): string {
  return `${score}/100`
}

/**
 * 格式化百分比显示
 */
export function formatPercentage(percentage: Percentage): string {
  return `${percentage}%`
}

/**
 * 格式化时间戳为本地时间
 */
export function formatTimestamp(timestamp: Timestamp, format: 'date' | 'time' | 'datetime' = 'datetime'): string {
  const date = new Date(timestamp)
  
  switch (format) {
    case 'date':
      return date.toLocaleDateString('zh-CN')
    case 'time':
      return date.toLocaleTimeString('zh-CN')
    case 'datetime':
      return date.toLocaleString('zh-CN')
    default:
      return date.toISOString()
  }
}

/**
 * 格式化用户名显示
 */
export function formatUserName(user: User): string {
  return user.name || user.email.split('@')[0] || '未知用户'
}

// ============================================================================
// 错误处理函数
// ============================================================================

/**
 * 安全地解析JSON
 */
export function safeParseJSON<T>(jsonString: string, fallback: T): T {
  try {
    return JSON.parse(jsonString)
  } catch {
    return fallback
  }
}

/**
 * 安全地访问嵌套属性
 */
export function safeGet<T>(obj: any, path: string, fallback: T): T {
  try {
    return path.split('.').reduce((current, key) => current?.[key], obj) ?? fallback
  } catch {
    return fallback
  }
}

/**
 * 验证并清理分数值
 */
export function sanitizeScore(value: any): Score {
  const num = Number(value)
  if (isNaN(num)) return 0 as Score
  return Math.max(0, Math.min(100, Math.round(num))) as Score
}

/**
 * 验证并清理百分比值
 */
export function sanitizePercentage(value: any): Percentage {
  const num = Number(value)
  if (isNaN(num)) return 0 as Percentage
  return Math.max(0, Math.min(100, Math.round(num))) as Percentage
}
