<script setup lang="ts">
import { ref, computed } from 'vue'

// AI Agent 配置
const attackerConfig = ref({
  aggressiveness: 70,
  intelligence: 85,
  stealth: 60,
  persistence: 75,
  strategy: 'adaptive'
})

const defenderConfig = ref({
  alertness: 80,
  responseSpeed: 90,
  coverage: 75,
  adaptability: 65,
  strategy: 'proactive'
})

// AI 状态
const aiStatus = ref({
  attacker: {
    status: 'active',
    currentAction: '端口扫描',
    nextAction: 'SQL注入尝试',
    confidence: 0.82,
    successRate: 0.65
  },
  defender: {
    status: 'monitoring',
    currentAction: '流量分析',
    nextAction: '异常检测',
    confidence: 0.78,
    blockRate: 0.73
  }
})

// 决策日志
const decisionLogs = ref([
  {
    id: 1,
    timestamp: new Date(Date.now() - 300000).toISOString(), // 5分钟前
    agent: 'attacker',
    decision: '选择SQL注入攻击',
    reasoning: '目标存在Web应用，检测到可能的注入点',
    confidence: 0.85,
    result: 'success'
  },
  {
    id: 2,
    timestamp: new Date(Date.now() - 295000).toISOString(), // 4分55秒前
    agent: 'defender',
    decision: '启用WAF规则',
    reasoning: '检测到SQL注入特征，触发防护机制',
    confidence: 0.92,
    result: 'blocked'
  },
  {
    id: 3,
    timestamp: new Date(Date.now() - 280000).toISOString(), // 4分40秒前
    agent: 'attacker',
    decision: '切换攻击向量',
    reasoning: 'SQL注入被阻断，尝试文件上传漏洞',
    confidence: 0.73,
    result: 'success'
  },
  {
    id: 4,
    timestamp: new Date(Date.now() - 260000).toISOString(), // 4分20秒前
    agent: 'defender',
    decision: '隔离受感染主机',
    reasoning: '检测到文件上传成功，立即隔离目标主机',
    confidence: 0.88,
    result: 'success'
  },
  {
    id: 5,
    timestamp: new Date(Date.now() - 120000).toISOString(), // 2分钟前
    agent: 'attacker',
    decision: '尝试横向移动',
    reasoning: '主机被隔离，寻找其他入口点进行横向移动',
    confidence: 0.67,
    result: 'pending'
  }
])

// 策略选项
const strategyOptions = {
  attacker: [
    { value: 'aggressive', label: '激进型', description: '快速、直接的攻击方式' },
    { value: 'stealth', label: '隐蔽型', description: '缓慢、隐蔽的渗透方式' },
    { value: 'adaptive', label: '自适应', description: '根据环境调整攻击策略' },
    { value: 'persistent', label: '持久型', description: '长期潜伏的APT攻击' }
  ],
  defender: [
    { value: 'reactive', label: '被动防御', description: '检测到威胁后响应' },
    { value: 'proactive', label: '主动防御', description: '预测并阻止潜在威胁' },
    { value: 'adaptive', label: '自适应', description: '根据攻击模式调整防御' },
    { value: 'zero_trust', label: '零信任', description: '严格验证所有访问' }
  ]
}

// 计算属性
const attackerEffectiveness = computed(() => {
  const { aggressiveness, intelligence, stealth, persistence } = attackerConfig.value
  return Math.round((aggressiveness + intelligence + stealth + persistence) / 4)
})

const defenderEffectiveness = computed(() => {
  const { alertness, responseSpeed, coverage, adaptability } = defenderConfig.value
  return Math.round((alertness + responseSpeed + coverage + adaptability) / 4)
})

// 方法
const updateAttackerConfig = () => {
  console.log('更新攻击者配置:', attackerConfig.value)
  // 这里会调用API更新AI配置
}

const updateDefenderConfig = () => {
  console.log('更新防御者配置:', defenderConfig.value)
  // 这里会调用API更新AI配置
}

const pauseAI = (agent: string) => {
  aiStatus.value[agent as keyof typeof aiStatus.value].status = 'paused'
  console.log(`暂停 ${agent} AI`)
}

const resumeAI = (agent: string) => {
  aiStatus.value[agent as keyof typeof aiStatus.value].status = 'active'
  console.log(`恢复 ${agent} AI`)
}

const resetAI = (agent: string) => {
  console.log(`重置 ${agent} AI`)
  // 重置AI状态和配置
}

const exportLogs = () => {
  console.log('导出决策日志')
  // 导出日志功能
}

const getStatusColor = (status: string) => {
  const colors = {
    active: '#00ff88',
    monitoring: '#00a3ff',
    paused: '#ffa500',
    error: '#ff4444'
  }
  return colors[status as keyof typeof colors] || '#ffffff'
}

const getResultColor = (result: string) => {
  const colors = {
    success: '#00ff88',
    blocked: '#ff4444',
    pending: '#ffa500'
  }
  return colors[result as keyof typeof colors] || '#ffffff'
}

const formatTimestamp = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}
</script>

<template>
  <div class="ai-control">
    <div class="header">
      <h1>AI Agent 控制台</h1>
      <p class="subtitle">监控和调整攻击/防御AI的行为参数</p>
    </div>

    <div class="control-grid">
      <!-- 攻击者AI控制 -->
      <div class="ai-panel attacker">
        <div class="panel-header">
          <h2>🔴 攻击者 AI</h2>
          <div class="ai-status">
            <span class="status-indicator" :style="{ backgroundColor: getStatusColor(aiStatus.attacker.status) }"></span>
            <span class="status-text">{{ aiStatus.attacker.status }}</span>
          </div>
        </div>

        <div class="ai-info">
          <div class="info-item">
            <span class="label">当前动作:</span>
            <span class="value">{{ aiStatus.attacker.currentAction }}</span>
          </div>
          <div class="info-item">
            <span class="label">下一步:</span>
            <span class="value">{{ aiStatus.attacker.nextAction }}</span>
          </div>
          <div class="info-item">
            <span class="label">置信度:</span>
            <span class="value">{{ (aiStatus.attacker.confidence * 100).toFixed(1) }}%</span>
          </div>
          <div class="info-item">
            <span class="label">成功率:</span>
            <span class="value">{{ (aiStatus.attacker.successRate * 100).toFixed(1) }}%</span>
          </div>
        </div>

        <div class="config-section">
          <h3>参数配置</h3>

          <div class="config-item">
            <label>攻击性 ({{ attackerConfig.aggressiveness }}%)</label>
            <input
              v-model="attackerConfig.aggressiveness"
              type="range"
              min="0"
              max="100"
              class="slider"
              @change="updateAttackerConfig"
            >
          </div>

          <div class="config-item">
            <label>智能度 ({{ attackerConfig.intelligence }}%)</label>
            <input
              v-model="attackerConfig.intelligence"
              type="range"
              min="0"
              max="100"
              class="slider"
              @change="updateAttackerConfig"
            >
          </div>

          <div class="config-item">
            <label>隐蔽性 ({{ attackerConfig.stealth }}%)</label>
            <input
              v-model="attackerConfig.stealth"
              type="range"
              min="0"
              max="100"
              class="slider"
              @change="updateAttackerConfig"
            >
          </div>

          <div class="config-item">
            <label>持久性 ({{ attackerConfig.persistence }}%)</label>
            <input
              v-model="attackerConfig.persistence"
              type="range"
              min="0"
              max="100"
              class="slider"
              @change="updateAttackerConfig"
            >
          </div>

          <div class="config-item">
            <label>攻击策略</label>
            <select v-model="attackerConfig.strategy" @change="updateAttackerConfig" class="strategy-select">
              <option v-for="strategy in strategyOptions.attacker" :key="strategy.value" :value="strategy.value">
                {{ strategy.label }}
              </option>
            </select>
          </div>

          <div class="effectiveness">
            <span class="label">综合效能:</span>
            <span class="value">{{ attackerEffectiveness }}%</span>
            <div class="effectiveness-bar">
              <div class="fill" :style="{ width: attackerEffectiveness + '%', backgroundColor: '#ff4444' }"></div>
            </div>
          </div>
        </div>

        <div class="control-actions">
          <button @click="pauseAI('attacker')" class="action-btn pause">暂停</button>
          <button @click="resumeAI('attacker')" class="action-btn resume">恢复</button>
          <button @click="resetAI('attacker')" class="action-btn reset">重置</button>
        </div>
      </div>

      <!-- 防御者AI控制 -->
      <div class="ai-panel defender">
        <div class="panel-header">
          <h2>🛡️ 防御者 AI</h2>
          <div class="ai-status">
            <span class="status-indicator" :style="{ backgroundColor: getStatusColor(aiStatus.defender.status) }"></span>
            <span class="status-text">{{ aiStatus.defender.status }}</span>
          </div>
        </div>

        <div class="ai-info">
          <div class="info-item">
            <span class="label">当前动作:</span>
            <span class="value">{{ aiStatus.defender.currentAction }}</span>
          </div>
          <div class="info-item">
            <span class="label">下一步:</span>
            <span class="value">{{ aiStatus.defender.nextAction }}</span>
          </div>
          <div class="info-item">
            <span class="label">置信度:</span>
            <span class="value">{{ (aiStatus.defender.confidence * 100).toFixed(1) }}%</span>
          </div>
          <div class="info-item">
            <span class="label">拦截率:</span>
            <span class="value">{{ (aiStatus.defender.blockRate * 100).toFixed(1) }}%</span>
          </div>
        </div>

        <div class="config-section">
          <h3>参数配置</h3>

          <div class="config-item">
            <label>警觉性 ({{ defenderConfig.alertness }}%)</label>
            <input
              v-model="defenderConfig.alertness"
              type="range"
              min="0"
              max="100"
              class="slider"
              @change="updateDefenderConfig"
            >
          </div>

          <div class="config-item">
            <label>响应速度 ({{ defenderConfig.responseSpeed }}%)</label>
            <input
              v-model="defenderConfig.responseSpeed"
              type="range"
              min="0"
              max="100"
              class="slider"
              @change="updateDefenderConfig"
            >
          </div>

          <div class="config-item">
            <label>覆盖范围 ({{ defenderConfig.coverage }}%)</label>
            <input
              v-model="defenderConfig.coverage"
              type="range"
              min="0"
              max="100"
              class="slider"
              @change="updateDefenderConfig"
            >
          </div>

          <div class="config-item">
            <label>适应性 ({{ defenderConfig.adaptability }}%)</label>
            <input
              v-model="defenderConfig.adaptability"
              type="range"
              min="0"
              max="100"
              class="slider"
              @change="updateDefenderConfig"
            >
          </div>

          <div class="config-item">
            <label>防御策略</label>
            <select v-model="defenderConfig.strategy" @change="updateDefenderConfig" class="strategy-select">
              <option v-for="strategy in strategyOptions.defender" :key="strategy.value" :value="strategy.value">
                {{ strategy.label }}
              </option>
            </select>
          </div>

          <div class="effectiveness">
            <span class="label">综合效能:</span>
            <span class="value">{{ defenderEffectiveness }}%</span>
            <div class="effectiveness-bar">
              <div class="fill" :style="{ width: defenderEffectiveness + '%', backgroundColor: '#00ff88' }"></div>
            </div>
          </div>
        </div>

        <div class="control-actions">
          <button @click="pauseAI('defender')" class="action-btn pause">暂停</button>
          <button @click="resumeAI('defender')" class="action-btn resume">恢复</button>
          <button @click="resetAI('defender')" class="action-btn reset">重置</button>
        </div>
      </div>

      <!-- 决策日志 -->
      <div class="decision-logs">
        <div class="logs-header">
          <h2>AI 决策日志</h2>
          <button @click="exportLogs" class="export-btn">导出日志</button>
        </div>

        <div class="logs-list">
          <div v-for="log in decisionLogs" :key="log.id" class="log-item">
            <div class="log-header">
              <span class="log-agent" :class="log.agent">{{ log.agent === 'attacker' ? '攻击者' : '防御者' }}</span>
              <span class="log-time">{{ formatTimestamp(log.timestamp) }}</span>
              <span class="log-result" :style="{ color: getResultColor(log.result) }">
                {{ log.result === 'success' ? '成功' : log.result === 'blocked' ? '被阻断' : '进行中' }}
              </span>
            </div>
            <div class="log-decision">{{ log.decision }}</div>
            <div class="log-reasoning">{{ log.reasoning }}</div>
            <div class="log-confidence">置信度: {{ (log.confidence * 100).toFixed(1) }}%</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.ai-control {
  padding: 2rem;
  max-width: 1600px;
  margin: 0 auto;
  color: white;
  padding-top: calc(70px + 2rem);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 3rem;

  h1 {
    color: #00ff88;
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
  }
}

.control-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 2rem;
}

.ai-panel {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 2rem;
  backdrop-filter: blur(10px);

  &.attacker {
    border: 1px solid rgba(255, 68, 68, 0.3);
  }

  &.defender {
    border: 1px solid rgba(0, 255, 136, 0.3);
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h2 {
      color: #00ff88;
      font-size: 1.3rem;
      margin: 0;
    }

    .ai-status {
      display: flex;
      align-items: center;
      gap: 0.5rem;

      .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
      }

      .status-text {
        color: white;
        font-size: 0.9rem;
        text-transform: capitalize;
      }
    }
  }

  .ai-info {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1.5rem;

    .info-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.5rem;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
      }

      .value {
        color: white;
        font-weight: 500;
        font-size: 0.9rem;
      }
    }
  }

  .config-section {
    h3 {
      color: #00ff88;
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .config-item {
      margin-bottom: 1rem;

      label {
        display: block;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
      }

      .slider {
        width: 100%;
        height: 6px;
        border-radius: 3px;
        background: rgba(255, 255, 255, 0.2);
        outline: none;
        cursor: pointer;

        &::-webkit-slider-thumb {
          appearance: none;
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: #00ff88;
          cursor: pointer;
        }

        &::-moz-range-thumb {
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: #00ff88;
          cursor: pointer;
          border: none;
        }
      }

      .strategy-select {
        width: 100%;
        padding: 0.5rem;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 5px;
        color: white;
        cursor: pointer;

        &:focus {
          outline: none;
          border-color: #00ff88;
        }

        option {
          background: #1a1a1a;
        }
      }
    }

    .effectiveness {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-top: 1.5rem;
      padding: 1rem;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 8px;

      .label {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
      }

      .value {
        color: white;
        font-weight: bold;
        font-size: 1.1rem;
      }

      .effectiveness-bar {
        flex: 1;
        height: 8px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        overflow: hidden;

        .fill {
          height: 100%;
          border-radius: 4px;
          transition: width 0.5s ease;
        }
      }
    }
  }

  .control-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;

    .action-btn {
      flex: 1;
      padding: 0.8rem;
      border: none;
      border-radius: 8px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      &.pause {
        background: #ffa500;
        color: white;
      }

      &.resume {
        background: #00ff88;
        color: black;
      }

      &.reset {
        background: transparent;
        color: #ff4444;
        border: 2px solid #ff4444;
      }

      &:hover {
        transform: translateY(-2px);
      }
    }
  }
}

.decision-logs {
  grid-column: 1 / -1;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 2rem;
  backdrop-filter: blur(10px);

  .logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h2 {
      color: #00ff88;
      font-size: 1.3rem;
      margin: 0;
    }

    .export-btn {
      padding: 0.5rem 1rem;
      background: transparent;
      color: #00ff88;
      border: 2px solid #00ff88;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #00ff88;
        color: black;
      }
    }
  }

  .logs-list {
    max-height: 400px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #00ff88;
      border-radius: 3px;
    }

    .log-item {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      padding: 1rem;
      border-left: 3px solid;

      &:has(.log-agent.attacker) {
        border-left-color: #ff4444;
      }

      &:has(.log-agent.defender) {
        border-left-color: #00ff88;
      }

      .log-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        .log-agent {
          padding: 0.2rem 0.5rem;
          border-radius: 4px;
          font-size: 0.8rem;
          font-weight: 500;

          &.attacker {
            background: rgba(255, 68, 68, 0.2);
            color: #ff4444;
          }

          &.defender {
            background: rgba(0, 255, 136, 0.2);
            color: #00ff88;
          }
        }

        .log-time {
          color: rgba(255, 255, 255, 0.6);
          font-size: 0.8rem;
          font-family: 'Courier New', monospace;
        }

        .log-result {
          font-size: 0.8rem;
          font-weight: 500;
        }
      }

      .log-decision {
        color: white;
        font-weight: 500;
        margin-bottom: 0.5rem;
      }

      .log-reasoning {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
        line-height: 1.4;
        margin-bottom: 0.5rem;
      }

      .log-confidence {
        color: #00ff88;
        font-size: 0.8rem;
      }
    }
  }
}

/* 桌面端适配 */
@media (min-width: 768px) {
  .ai-control {
    padding: 3rem;
    padding-top: calc(80px + 3rem);
  }

  .header h1 {
    font-size: 3rem;
  }
}

@media (max-width: 1200px) {
  .control-grid {
    grid-template-columns: 1fr;
  }
}
</style>
