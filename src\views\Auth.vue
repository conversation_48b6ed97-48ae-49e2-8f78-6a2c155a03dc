<script setup lang="ts">
import { ref } from 'vue'
import { useAuthStore } from '../store'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

const isLogin = ref(true)
const email = ref('')
const password = ref('')
const confirmPassword = ref('')
const error = ref('')

const handleSubmit = async () => {
  try {
    if (!isLogin.value && password.value !== confirmPassword.value) {
      error.value = 'Passwords do not match'
      return
    }

    // Simulate authentication
    const userData = {
      id: '1',
      email: email.value,
      name: email.value.split('@')[0]
    }

    authStore.login(userData)
    router.push('/challenges')
  } catch (e) {
    error.value = 'Authentication failed'
  }
}
</script>

<template>
  <div class="auth-container">
    <div class="auth-card">
      <h2>{{ isLogin ? 'Login' : 'Sign Up' }}</h2>
      
      <form @submit.prevent="handleSubmit" class="auth-form">
        <div class="form-group">
          <label for="email">Email</label>
          <input 
            id="email"
            v-model="email"
            type="email"
            required
            placeholder="Enter your email"
          >
        </div>

        <div class="form-group">
          <label for="password">Password</label>
          <input 
            id="password"
            v-model="password"
            type="password"
            required
            placeholder="Enter your password"
          >
        </div>

        <div v-if="!isLogin" class="form-group">
          <label for="confirmPassword">Confirm Password</label>
          <input 
            id="confirmPassword"
            v-model="confirmPassword"
            type="password"
            required
            placeholder="Confirm your password"
          >
        </div>

        <p v-if="error" class="error">{{ error }}</p>

        <button type="submit" class="submit-btn">
          {{ isLogin ? 'Login' : 'Sign Up' }}
        </button>
      </form>

      <p class="toggle-auth">
        {{ isLogin ? "Don't have an account?" : 'Already have an account?' }}
        <button @click="isLogin = !isLogin" class="toggle-btn">
          {{ isLogin ? 'Sign Up' : 'Login' }}
        </button>
      </p>
    </div>
  </div>
</template>

<style scoped lang="scss">
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.auth-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 10px;
  width: 100%;
  max-width: 400px;

  h2 {
    color: #00ff88;
    text-align: center;
    margin-bottom: 2rem;
  }
}

.auth-form {
  .form-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: white;
    }

    input {
      width: 100%;
      padding: 0.8rem;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 5px;
      color: white;

      &:focus {
        outline: none;
        border-color: #00ff88;
      }
    }
  }
}

.error {
  color: #ff4444;
  margin-bottom: 1rem;
  text-align: center;
}

.submit-btn {
  width: 100%;
  padding: 0.8rem;
  background: linear-gradient(45deg, #00ff88, #00a3ff);
  border: none;
  border-radius: 5px;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.02);
  }
}

.toggle-auth {
  margin-top: 1.5rem;
  text-align: center;
  color: white;

  .toggle-btn {
    background: none;
    border: none;
    color: #00ff88;
    cursor: pointer;
    padding: 0 0.5rem;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>