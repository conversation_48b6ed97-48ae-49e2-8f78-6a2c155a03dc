<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useAuthStore } from '../store'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

const isLogin = ref(true)
const email = ref('')
const password = ref('')
const confirmPassword = ref('')
const name = ref('')

// 表单验证
const emailError = ref('')
const passwordError = ref('')
const confirmPasswordError = ref('')

// 计算属性
const isFormValid = computed(() => {
  if (isLogin.value) {
    return email.value && password.value && !emailError.value && !passwordError.value
  } else {
    return email.value && password.value && confirmPassword.value &&
           !emailError.value && !passwordError.value && !confirmPasswordError.value
  }
})

const isLoading = computed(() => authStore.loading)

// 验证邮箱格式
const validateEmail = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!email.value) {
    emailError.value = '请输入邮箱地址'
  } else if (!emailRegex.test(email.value)) {
    emailError.value = '请输入有效的邮箱地址'
  } else {
    emailError.value = ''
  }
}

// 验证密码
const validatePassword = () => {
  if (!password.value) {
    passwordError.value = '请输入密码'
  } else if (password.value.length < 6) {
    passwordError.value = '密码至少需要6个字符'
  } else {
    passwordError.value = ''
  }
}

// 验证确认密码
const validateConfirmPassword = () => {
  if (!isLogin.value) {
    if (!confirmPassword.value) {
      confirmPasswordError.value = '请确认密码'
    } else if (password.value !== confirmPassword.value) {
      confirmPasswordError.value = '密码不匹配'
    } else {
      confirmPasswordError.value = ''
    }
  }
}

// 监听表单切换，清除错误
watch(isLogin, () => {
  authStore.clearError()
  emailError.value = ''
  passwordError.value = ''
  confirmPasswordError.value = ''
})

// 提交表单
const handleSubmit = async () => {
  // 验证表单
  validateEmail()
  validatePassword()
  if (!isLogin.value) {
    validateConfirmPassword()
  }

  if (!isFormValid.value) {
    return
  }

  try {
    if (isLogin.value) {
      await authStore.login({
        email: email.value,
        name: name.value
      })
    } else {
      await authStore.register({
        email: email.value,
        password: password.value,
        name: name.value || email.value.split('@')[0]
      })
    }

    // 登录成功后跳转
    router.push('/challenges')
  } catch (error) {
    // 错误已在store中处理
    console.error('Authentication error:', error)
  }
}

// 切换登录/注册模式
const toggleMode = () => {
  isLogin.value = !isLogin.value
  // 清除表单数据
  email.value = ''
  password.value = ''
  confirmPassword.value = ''
  name.value = ''
  // 清除错误信息
  authStore.clearError()
  emailError.value = ''
  passwordError.value = ''
  confirmPasswordError.value = ''
}
</script>

<template>
  <div class="auth-container">
    <div class="auth-card">
      <h2>{{ isLogin ? '登录' : '注册' }}</h2>

      <form @submit.prevent="handleSubmit" class="auth-form">
        <!-- 邮箱输入 -->
        <div class="form-group">
          <label for="email">邮箱</label>
          <input
            id="email"
            v-model="email"
            type="email"
            :class="{ 'error': emailError }"
            placeholder="请输入您的邮箱"
            @blur="validateEmail"
            :disabled="isLoading"
          >
          <span v-if="emailError" class="field-error">{{ emailError }}</span>
        </div>

        <!-- 用户名输入（仅注册时显示） -->
        <div v-if="!isLogin" class="form-group">
          <label for="name">用户名（可选）</label>
          <input
            id="name"
            v-model="name"
            type="text"
            placeholder="请输入用户名，留空将使用邮箱前缀"
            :disabled="isLoading"
          >
        </div>

        <!-- 密码输入 -->
        <div class="form-group">
          <label for="password">密码</label>
          <input
            id="password"
            v-model="password"
            type="password"
            :class="{ 'error': passwordError }"
            placeholder="请输入您的密码"
            @blur="validatePassword"
            :disabled="isLoading"
          >
          <span v-if="passwordError" class="field-error">{{ passwordError }}</span>
        </div>

        <!-- 确认密码输入（仅注册时显示） -->
        <div v-if="!isLogin" class="form-group">
          <label for="confirmPassword">确认密码</label>
          <input
            id="confirmPassword"
            v-model="confirmPassword"
            type="password"
            :class="{ 'error': confirmPasswordError }"
            placeholder="请再次输入密码"
            @blur="validateConfirmPassword"
            :disabled="isLoading"
          >
          <span v-if="confirmPasswordError" class="field-error">{{ confirmPasswordError }}</span>
        </div>

        <!-- 全局错误信息 -->
        <p v-if="authStore.error" class="error">{{ authStore.error }}</p>

        <!-- 提交按钮 -->
        <button
          type="submit"
          class="submit-btn"
          :disabled="!isFormValid || isLoading"
          :class="{ 'loading': isLoading }"
        >
          <span v-if="isLoading" class="loading-spinner"></span>
          {{ isLoading ? '处理中...' : (isLogin ? '登录' : '注册') }}
        </button>
      </form>

      <!-- 切换登录/注册 -->
      <p class="toggle-auth">
        {{ isLogin ? "还没有账户？" : '已有账户？' }}
        <button @click="toggleMode" class="toggle-btn" :disabled="isLoading">
          {{ isLogin ? '注册' : '登录' }}
        </button>
      </p>
    </div>
  </div>
</template>

<style scoped lang="scss">
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  padding-top: calc(70px + 2rem); /* 为导航栏留出空间 */
}

.auth-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 10px;
  width: 100%;
  max-width: 400px;

  h2 {
    color: #00ff88;
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2rem;
  }
}

/* 桌面端适配 */
@media (min-width: 768px) {
  .auth-container {
    padding-top: calc(80px + 2rem);
  }

  .auth-card {
    padding: 3rem;
    max-width: 500px;

    h2 {
      font-size: 2.5rem;
      margin-bottom: 3rem;
    }
  }
}

@media (min-width: 1200px) {
  .auth-card {
    padding: 4rem;
    max-width: 600px;

    h2 {
      font-size: 3rem;
    }
  }
}

.auth-form {
  .form-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: white;
      font-size: 1rem;
      font-weight: 500;
    }

    input {
      width: 100%;
      padding: 0.8rem;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 5px;
      color: white;
      font-size: 1rem;
      box-sizing: border-box;
      transition: border-color 0.3s, box-shadow 0.3s;

      &:focus {
        outline: none;
        border-color: #00ff88;
        box-shadow: 0 0 0 2px rgba(0, 255, 136, 0.2);
      }

      &::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }

      &.error {
        border-color: #ff4444;
        box-shadow: 0 0 0 2px rgba(255, 68, 68, 0.2);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    .field-error {
      display: block;
      color: #ff4444;
      font-size: 0.85rem;
      margin-top: 0.3rem;
    }
  }
}

.error {
  color: #ff4444;
  margin-bottom: 1rem;
  text-align: center;
  font-size: 1rem;
}

.submit-btn {
  width: 100%;
  padding: 0.8rem;
  background: linear-gradient(45deg, #00ff88, #00a3ff);
  border: none;
  border-radius: 5px;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s, opacity 0.3s;
  font-size: 1rem;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;

  &:hover:not(:disabled) {
    transform: scale(1.02);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  &.loading {
    pointer-events: none;
  }

  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 桌面端表单适配 */
@media (min-width: 768px) {
  .auth-form {
    .form-group {
      margin-bottom: 2rem;

      label {
        font-size: 1.1rem;
        margin-bottom: 0.8rem;
      }

      input {
        padding: 1rem;
        font-size: 1.1rem;
      }
    }
  }

  .submit-btn {
    padding: 1rem;
    font-size: 1.1rem;
  }

  .error {
    font-size: 1.1rem;
  }
}

@media (min-width: 1200px) {
  .auth-form {
    .form-group {
      label {
        font-size: 1.2rem;
      }

      input {
        padding: 1.2rem;
        font-size: 1.2rem;
      }
    }
  }

  .submit-btn {
    padding: 1.2rem;
    font-size: 1.2rem;
  }
}

.toggle-auth {
  margin-top: 1.5rem;
  text-align: center;
  color: white;

  .toggle-btn {
    background: none;
    border: none;
    color: #00ff88;
    cursor: pointer;
    padding: 0 0.5rem;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>