<script setup lang="ts">
import { ref } from 'vue'
import { useAuthStore } from '../store'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

const isLogin = ref(true)
const email = ref('')
const password = ref('')
const confirmPassword = ref('')
const error = ref('')

const handleSubmit = async () => {
  try {
    if (!isLogin.value && password.value !== confirmPassword.value) {
      error.value = '密码不匹配'
      return
    }

    // Simulate authentication
    const userData = {
      id: '1',
      email: email.value,
      name: email.value.split('@')[0]
    }

    authStore.login(userData)
    router.push('/challenges')
  } catch (e) {
    error.value = '认证失败'
  }
}
</script>

<template>
  <div class="auth-container">
    <div class="auth-card">
      <h2>{{ isLogin ? '登录' : '注册' }}</h2>

      <form @submit.prevent="handleSubmit" class="auth-form">
        <div class="form-group">
          <label for="email">邮箱</label>
          <input
            id="email"
            v-model="email"
            type="email"
            required
            placeholder="请输入您的邮箱"
          >
        </div>

        <div class="form-group">
          <label for="password">密码</label>
          <input
            id="password"
            v-model="password"
            type="password"
            required
            placeholder="请输入您的密码"
          >
        </div>

        <div v-if="!isLogin" class="form-group">
          <label for="confirmPassword">确认密码</label>
          <input
            id="confirmPassword"
            v-model="confirmPassword"
            type="password"
            required
            placeholder="请再次输入密码"
          >
        </div>

        <p v-if="error" class="error">{{ error }}</p>

        <button type="submit" class="submit-btn">
          {{ isLogin ? '登录' : '注册' }}
        </button>
      </form>

      <p class="toggle-auth">
        {{ isLogin ? "还没有账户？" : '已有账户？' }}
        <button @click="isLogin = !isLogin" class="toggle-btn">
          {{ isLogin ? '注册' : '登录' }}
        </button>
      </p>
    </div>
  </div>
</template>

<style scoped lang="scss">
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.auth-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 10px;
  width: 100%;
  max-width: 400px;

  h2 {
    color: #00ff88;
    text-align: center;
    margin-bottom: 2rem;
  }
}

.auth-form {
  .form-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: white;
    }

    input {
      width: 100%;
      padding: 0.8rem;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 5px;
      color: white;

      &:focus {
        outline: none;
        border-color: #00ff88;
      }
    }
  }
}

.error {
  color: #ff4444;
  margin-bottom: 1rem;
  text-align: center;
}

.submit-btn {
  width: 100%;
  padding: 0.8rem;
  background: linear-gradient(45deg, #00ff88, #00a3ff);
  border: none;
  border-radius: 5px;
  color: white;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s;

  &:hover {
    transform: scale(1.02);
  }
}

.toggle-auth {
  margin-top: 1.5rem;
  text-align: center;
  color: white;

  .toggle-btn {
    background: none;
    border: none;
    color: #00ff88;
    cursor: pointer;
    padding: 0 0.5rem;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>