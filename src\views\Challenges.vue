<script setup lang="ts">
import { ref, computed } from 'vue'

interface Challenge {
  id: number
  title: string
  category: string
  difficulty: string
  points: number
  solved: boolean
  description: string
}

const categories = [
  'Web 安全',
  '密码学',
  '逆向工程',
  '二进制漏洞',
  '取证分析',
  '全部'
]

const selectedCategory = ref('全部')
const showDeployModal = ref(false)
const selectedChallenge = ref<Challenge | null>(null)
const flagInput = ref('')

const challenges: Challenge[] = [
  {
    id: 1,
    title: 'SQL 注入基础',
    category: 'Web 安全',
    difficulty: '简单',
    points: 100,
    solved: false,
    description: '学习 SQL 注入的基础知识，利用漏洞登录表单。'
  },
  {
    id: 2,
    title: '经典凯撒密码',
    category: '密码学',
    difficulty: '简单',
    points: 50,
    solved: false,
    description: '解密使用凯撒密码编码的消息。'
  }
]

const filteredChallenges = computed(() => {
  if (selectedCategory.value === '全部') {
    return challenges
  }
  return challenges.filter(challenge => challenge.category === selectedCategory.value)
})

const deployChallenge = (challenge: Challenge) => {
  selectedChallenge.value = challenge
  showDeployModal.value = true
}

const submitFlag = () => {
  console.log('Submitting flag:', flagInput.value)
  showDeployModal.value = false
  flagInput.value = ''
  selectedChallenge.value = null
}

const closeModal = () => {
  showDeployModal.value = false
  flagInput.value = ''
  selectedChallenge.value = null
}
</script>

<template>
  <div class="challenges">
    <h1>CTF 挑战题目</h1>

    <!-- Category Filter -->
    <div class="categories">
      <button
        v-for="category in categories"
        :key="category"
        :class="{ active: selectedCategory === category }"
        @click="selectedCategory = category"
      >
        {{ category }}
      </button>
    </div>

    <!-- Challenge Grid -->
    <div class="challenge-grid">
      <div
        v-for="challenge in filteredChallenges"
        :key="challenge.id"
        class="challenge-card"
        :class="{ solved: challenge.solved }"
      >
        <div class="challenge-header">
          <span class="category-tag">{{ challenge.category }}</span>
          <span class="points">{{ challenge.points }} 分</span>
        </div>
        <h3>{{ challenge.title }}</h3>
        <div class="difficulty" :class="challenge.difficulty">
          {{ challenge.difficulty }}
        </div>
        <p>{{ challenge.description }}</p>
        <button @click="deployChallenge(challenge)" class="deploy-btn">
          部署挑战
        </button>
      </div>
    </div>

    <!-- Deploy Modal -->
    <Teleport to="body">
      <div v-if="showDeployModal" class="modal-overlay">
        <div class="modal">
          <h2>{{ selectedChallenge?.title }}</h2>
          <div class="deployment-info">
            <p>挑战环境正在准备中...</p>
            <div class="connection-details">
              <p>连接详情：</p>
              <code>http://challenge-{{ selectedChallenge?.id }}.ctf.local:8080</code>
            </div>
          </div>
          <div class="flag-submission">
            <input
              v-model="flagInput"
              type="text"
              placeholder="输入 flag (例如: CTF{flag})"
            >
            <button @click="submitFlag" class="submit-btn">提交 Flag</button>
          </div>
          <button @click="closeModal" class="close-btn">关闭</button>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<style scoped lang="scss">
.challenges {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  color: white;
  padding-top: calc(70px + 2rem); /* 为导航栏留出空间 */
  min-height: 100vh;
}

h1 {
  text-align: center;
  margin-bottom: 2rem;
  color: #00ff88;
  font-size: 2.5rem;
}

/* 桌面端适配 */
@media (min-width: 768px) {
  .challenges {
    padding: 4rem;
    max-width: 1400px;
    padding-top: calc(80px + 4rem);
  }

  h1 {
    font-size: 3.5rem;
    margin-bottom: 3rem;
  }
}

@media (min-width: 1200px) {
  .challenges {
    padding: 6rem;
    max-width: 1600px;
  }

  h1 {
    font-size: 4rem;
  }
}

.categories {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  justify-content: center;

  button {
    padding: 0.5rem 1rem;
    border: 2px solid #00ff88;
    background: transparent;
    color: #00ff88;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 0.9rem;

    &.active {
      background: #00ff88;
      color: black;
    }

    &:hover {
      background: #00ff88;
      color: black;
    }
  }
}

.challenge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

/* 桌面端分类和网格适配 */
@media (min-width: 768px) {
  .categories {
    gap: 1.5rem;
    margin-bottom: 3rem;

    button {
      padding: 0.8rem 1.5rem;
      font-size: 1rem;
    }
  }

  .challenge-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 3rem;
  }
}

@media (min-width: 1200px) {
  .categories {
    gap: 2rem;

    button {
      padding: 1rem 2rem;
      font-size: 1.1rem;
    }
  }

  .challenge-grid {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 4rem;
  }
}

.challenge-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 1.5rem;
  transition: transform 0.3s;
  backdrop-filter: blur(10px);

  &:hover {
    transform: translateY(-5px);
  }

  &.solved {
    border: 2px solid #00ff88;
  }

  .challenge-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .category-tag {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
    padding: 0.2rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
  }

  .points {
    color: #00ff88;
    font-weight: bold;
    font-size: 0.9rem;
  }

  h3 {
    margin: 0.5rem 0;
    color: white;
    font-size: 1.2rem;
  }

  p {
    font-size: 0.9rem;
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.8);
  }
}

/* 桌面端挑战卡片适配 */
@media (min-width: 768px) {
  .challenge-card {
    padding: 2rem;

    .category-tag {
      font-size: 0.9rem;
      padding: 0.3rem 0.8rem;
    }

    .points {
      font-size: 1rem;
    }

    h3 {
      font-size: 1.4rem;
      margin: 1rem 0;
    }

    p {
      font-size: 1rem;
    }
  }
}

@media (min-width: 1200px) {
  .challenge-card {
    padding: 2.5rem;

    h3 {
      font-size: 1.6rem;
    }

    p {
      font-size: 1.1rem;
    }
  }
}

  .difficulty {
    display: inline-block;
    padding: 0.2rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    margin: 0.5rem 0;

    &.简单 {
      background: rgba(0, 255, 0, 0.2);
      color: #00ff00;
    }

    &.中等 {
      background: rgba(255, 166, 0, 0.2);
      color: orange;
    }

    &.困难 {
      background: rgba(255, 0, 0, 0.2);
      color: #ff0000;
    }
  }

  .deploy-btn {
    width: 100%;
    margin-top: 1rem;
    background: linear-gradient(45deg, #00ff88, #00a3ff);
    border: none;
    color: white;
    padding: 0.8rem;
    border-radius: 5px;
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
      transform: scale(1.02);
    }
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: #1a1a1a;
  padding: 2rem;
  border-radius: 10px;
  width: 90%;
  max-width: 500px;
  position: relative;

  h2 {
    color: #00ff88;
    margin-bottom: 1.5rem;
  }

  .deployment-info {
    margin: 1.5rem 0;

    .connection-details {
      background: rgba(0, 0, 0, 0.3);
      padding: 1rem;
      border-radius: 5px;
      margin-top: 1rem;

      code {
        color: #00ff88;
        display: block;
        margin-top: 0.5rem;
      }
    }
  }

  .flag-submission {
    display: flex;
    gap: 1rem;
    margin: 1.5rem 0;

    input {
      flex: 1;
      padding: 0.8rem;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 5px;
      color: white;

      &:focus {
        border-color: #00ff88;
        outline: none;
      }
    }

    .submit-btn {
      background: #00ff88;
      color: black;
      border: none;
      padding: 0.8rem 1.5rem;
      border-radius: 5px;
      cursor: pointer;
      transition: transform 0.2s;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .close-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 1.5rem;
    padding: 0.5rem;

    &:hover {
      color: #00ff88;
    }
  }
}
</style>