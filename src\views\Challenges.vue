<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useChallengeStore } from '../store'

const challengeStore = useChallengeStore()

const categories = [
  'Web 安全',
  '密码学',
  '逆向工程',
  '二进制漏洞',
  '取证分析',
  '全部'
]

const showDeployModal = ref(false)
const selectedChallenge = ref<any>(null)
const flagInput = ref('')
const flagError = ref('')
const flagSuccess = ref('')
const isSubmitting = ref(false)

// 计算属性
const filteredChallenges = computed(() => challengeStore.filteredChallenges)
const selectedCategory = computed({
  get: () => challengeStore.selectedCategory,
  set: (value) => challengeStore.setSelectedCategory(value)
})

// 部署挑战
const deployChallenge = (challenge: any) => {
  selectedChallenge.value = challenge
  showDeployModal.value = true
  flagInput.value = ''
  flagError.value = ''
  flagSuccess.value = ''
}

// 提交Flag
const submitFlag = async () => {
  if (!flagInput.value.trim()) {
    flagError.value = '请输入 Flag'
    return
  }

  if (!selectedChallenge.value) {
    return
  }

  isSubmitting.value = true
  flagError.value = ''
  flagSuccess.value = ''

  try {
    const result = await challengeStore.submitFlag(
      selectedChallenge.value.id,
      flagInput.value.trim()
    )

    if (result.success) {
      flagSuccess.value = result.message
      // 延迟关闭模态框，让用户看到成功消息
      setTimeout(() => {
        closeModal()
      }, 2000)
    } else {
      flagError.value = result.message
    }
  } catch (error) {
    flagError.value = '提交失败，请重试'
  } finally {
    isSubmitting.value = false
  }
}

// 关闭模态框
const closeModal = () => {
  showDeployModal.value = false
  flagInput.value = ''
  flagError.value = ''
  flagSuccess.value = ''
  selectedChallenge.value = null
}

// 获取难度颜色类
const getDifficultyClass = (difficulty: string) => {
  const classMap: { [key: string]: string } = {
    '简单': 'easy',
    '中等': 'medium',
    '困难': 'hard'
  }
  return classMap[difficulty] || 'easy'
}

// 格式化连接地址
const getConnectionUrl = (challengeId: number) => {
  return `http://challenge-${challengeId}.ctf.local:8080`
}

// 复制URL到剪贴板
const copyUrl = async () => {
  if (!selectedChallenge.value) return

  const url = getConnectionUrl(selectedChallenge.value.id)
  try {
    await navigator.clipboard.writeText(url)
    // 可以添加一个临时的成功提示
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = url
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
  }
}

onMounted(() => {
  // 确保挑战数据已初始化
  if (challengeStore.challenges.length === 0) {
    challengeStore.initChallenges()
  }
})
</script>

<template>
  <div class="challenges">
    <h1>CTF 挑战题目</h1>

    <!-- Category Filter -->
    <div class="categories">
      <button
        v-for="category in categories"
        :key="category"
        :class="{ active: selectedCategory === category }"
        @click="selectedCategory = category"
      >
        {{ category }}
      </button>
    </div>

    <!-- Challenge Grid -->
    <div class="challenge-grid">
      <div
        v-for="challenge in filteredChallenges"
        :key="challenge.id"
        class="challenge-card"
        :class="{ solved: challenge.solved }"
      >
        <div class="challenge-header">
          <span class="category-tag">{{ challenge.category }}</span>
          <span class="points">{{ challenge.points }} 分</span>
        </div>
        <h3>{{ challenge.title }}</h3>
        <div class="difficulty" :class="getDifficultyClass(challenge.difficulty)">
          {{ challenge.difficulty }}
        </div>
        <p>{{ challenge.description }}</p>
        <div class="challenge-actions">
          <button
            @click="deployChallenge(challenge)"
            class="deploy-btn"
            :disabled="challenge.solved"
          >
            {{ challenge.solved ? '已完成' : '部署挑战' }}
          </button>
          <span v-if="challenge.solved" class="solved-badge">✓</span>
        </div>
      </div>
    </div>

    <!-- Deploy Modal -->
    <Teleport to="body">
      <div v-if="showDeployModal" class="modal-overlay" @click="closeModal">
        <div class="modal" @click.stop>
          <div class="modal-header">
            <h2>{{ selectedChallenge?.title }}</h2>
            <button @click="closeModal" class="close-btn">×</button>
          </div>

          <div class="modal-content">
            <div class="challenge-info">
              <div class="info-item">
                <span class="label">分类：</span>
                <span class="value">{{ selectedChallenge?.category }}</span>
              </div>
              <div class="info-item">
                <span class="label">难度：</span>
                <span class="value difficulty" :class="getDifficultyClass(selectedChallenge?.difficulty)">
                  {{ selectedChallenge?.difficulty }}
                </span>
              </div>
              <div class="info-item">
                <span class="label">分值：</span>
                <span class="value">{{ selectedChallenge?.points }} 分</span>
              </div>
            </div>

            <div class="deployment-info">
              <h3>环境信息</h3>
              <p class="status">✅ 挑战环境已准备就绪</p>
              <div class="connection-details">
                <p>连接地址：</p>
                <div class="url-container">
                  <code>{{ getConnectionUrl(selectedChallenge?.id) }}</code>
                  <button class="copy-btn" @click="copyUrl">复制</button>
                </div>
              </div>
            </div>

            <div class="flag-submission">
              <h3>提交 Flag</h3>
              <div class="input-group">
                <input
                  v-model="flagInput"
                  type="text"
                  placeholder="输入 flag (例如: CTF{flag})"
                  :disabled="isSubmitting"
                  @keyup.enter="submitFlag"
                  class="flag-input"
                >
                <button
                  @click="submitFlag"
                  class="submit-btn"
                  :disabled="isSubmitting || !flagInput.trim()"
                  :class="{ 'loading': isSubmitting }"
                >
                  <span v-if="isSubmitting" class="loading-spinner"></span>
                  {{ isSubmitting ? '验证中...' : '提交 Flag' }}
                </button>
              </div>

              <!-- 错误信息 -->
              <p v-if="flagError" class="flag-error">{{ flagError }}</p>

              <!-- 成功信息 -->
              <p v-if="flagSuccess" class="flag-success">{{ flagSuccess }}</p>
            </div>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<style scoped lang="scss">
.challenges {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  color: white;
  padding-top: calc(70px + 2rem); /* 为导航栏留出空间 */
  min-height: 100vh;
}

h1 {
  text-align: center;
  margin-bottom: 2rem;
  color: #00ff88;
  font-size: 2.5rem;
}

/* 桌面端适配 */
@media (min-width: 768px) {
  .challenges {
    padding: 4rem;
    max-width: 1400px;
    padding-top: calc(80px + 4rem);
  }

  h1 {
    font-size: 3.5rem;
    margin-bottom: 3rem;
  }
}

@media (min-width: 1200px) {
  .challenges {
    padding: 6rem;
    max-width: 1600px;
  }

  h1 {
    font-size: 4rem;
  }
}

.categories {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  justify-content: center;

  button {
    padding: 0.5rem 1rem;
    border: 2px solid #00ff88;
    background: transparent;
    color: #00ff88;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 0.9rem;

    &.active {
      background: #00ff88;
      color: black;
    }

    &:hover {
      background: #00ff88;
      color: black;
    }
  }
}

.challenge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

/* 桌面端分类和网格适配 */
@media (min-width: 768px) {
  .categories {
    gap: 1.5rem;
    margin-bottom: 3rem;

    button {
      padding: 0.8rem 1.5rem;
      font-size: 1rem;
    }
  }

  .challenge-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 3rem;
  }
}

@media (min-width: 1200px) {
  .categories {
    gap: 2rem;

    button {
      padding: 1rem 2rem;
      font-size: 1.1rem;
    }
  }

  .challenge-grid {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 4rem;
  }
}

.challenge-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 1.5rem;
  transition: transform 0.3s;
  backdrop-filter: blur(10px);

  &:hover {
    transform: translateY(-5px);
  }

  &.solved {
    border: 2px solid #00ff88;
  }

  .challenge-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .category-tag {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
    padding: 0.2rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
  }

  .points {
    color: #00ff88;
    font-weight: bold;
    font-size: 0.9rem;
  }

  h3 {
    margin: 0.5rem 0;
    color: white;
    font-size: 1.2rem;
  }

  p {
    font-size: 0.9rem;
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.8);
  }
}

/* 桌面端挑战卡片适配 */
@media (min-width: 768px) {
  .challenge-card {
    padding: 2rem;

    .category-tag {
      font-size: 0.9rem;
      padding: 0.3rem 0.8rem;
    }

    .points {
      font-size: 1rem;
    }

    h3 {
      font-size: 1.4rem;
      margin: 1rem 0;
    }

    p {
      font-size: 1rem;
    }
  }
}

@media (min-width: 1200px) {
  .challenge-card {
    padding: 2.5rem;

    h3 {
      font-size: 1.6rem;
    }

    p {
      font-size: 1.1rem;
    }
  }
}

  .difficulty {
    display: inline-block;
    padding: 0.2rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    margin: 0.5rem 0;

    &.简单 {
      background: rgba(0, 255, 0, 0.2);
      color: #00ff00;
    }

    &.中等 {
      background: rgba(255, 166, 0, 0.2);
      color: orange;
    }

    &.困难 {
      background: rgba(255, 0, 0, 0.2);
      color: #ff0000;
    }
  }

  .challenge-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
  }

  .deploy-btn {
    flex: 1;
    background: linear-gradient(45deg, #00ff88, #00a3ff);
    border: none;
    color: white;
    padding: 0.8rem;
    border-radius: 5px;
    cursor: pointer;
    transition: transform 0.2s, opacity 0.3s;

    &:hover:not(:disabled) {
      transform: scale(1.02);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      background: rgba(255, 255, 255, 0.2);
    }
  }

  .solved-badge {
    color: #00ff88;
    font-size: 1.2rem;
    font-weight: bold;
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: #1a1a1a;
  border-radius: 15px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  border: 1px solid rgba(0, 255, 136, 0.2);

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    h2 {
      color: #00ff88;
      margin: 0;
      font-size: 1.5rem;
    }

    .close-btn {
      background: none;
      border: none;
      color: rgba(255, 255, 255, 0.6);
      cursor: pointer;
      font-size: 2rem;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.3s;

      &:hover {
        color: #ff4444;
        background: rgba(255, 68, 68, 0.1);
      }
    }
  }

  .modal-content {
    padding: 2rem;
  }

  .challenge-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;

    .info-item {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .label {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
      }

      .value {
        color: white;
        font-weight: 500;

        &.difficulty {
          display: inline-block;
          padding: 0.2rem 0.8rem;
          border-radius: 15px;
          font-size: 0.8rem;
          width: fit-content;

          &.easy {
            background: rgba(0, 255, 0, 0.2);
            color: #00ff00;
          }

          &.medium {
            background: rgba(255, 166, 0, 0.2);
            color: orange;
          }

          &.hard {
            background: rgba(255, 0, 0, 0.2);
            color: #ff0000;
          }
        }
      }
    }
  }

  .deployment-info {
    margin-bottom: 2rem;

    h3 {
      color: #00ff88;
      margin-bottom: 1rem;
      font-size: 1.2rem;
    }

    .status {
      color: #00ff88;
      margin-bottom: 1rem;
      font-weight: 500;
    }

    .connection-details {
      background: rgba(0, 0, 0, 0.3);
      padding: 1rem;
      border-radius: 8px;
      border: 1px solid rgba(0, 255, 136, 0.2);

      p {
        margin-bottom: 0.5rem;
        color: rgba(255, 255, 255, 0.8);
      }

      .url-container {
        display: flex;
        gap: 0.5rem;
        align-items: center;

        code {
          flex: 1;
          color: #00ff88;
          background: rgba(0, 255, 136, 0.1);
          padding: 0.5rem;
          border-radius: 4px;
          font-family: 'Courier New', monospace;
          word-break: break-all;
        }

        .copy-btn {
          background: rgba(0, 255, 136, 0.2);
          color: #00ff88;
          border: 1px solid #00ff88;
          padding: 0.5rem 1rem;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.3s;
          font-size: 0.9rem;

          &:hover {
            background: #00ff88;
            color: black;
          }
        }
      }
    }
  }

  .flag-submission {
    h3 {
      color: #00ff88;
      margin-bottom: 1rem;
      font-size: 1.2rem;
    }

    .input-group {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;

      .flag-input {
        flex: 1;
        padding: 0.8rem;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 5px;
        color: white;
        font-size: 1rem;
        transition: border-color 0.3s;

        &:focus {
          border-color: #00ff88;
          outline: none;
          box-shadow: 0 0 0 2px rgba(0, 255, 136, 0.2);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }

      .submit-btn {
        background: linear-gradient(45deg, #00ff88, #00a3ff);
        color: white;
        border: none;
        padding: 0.8rem 1.5rem;
        border-radius: 5px;
        cursor: pointer;
        transition: transform 0.2s, opacity 0.3s;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 500;

        &:hover:not(:disabled) {
          transform: scale(1.05);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        &.loading {
          pointer-events: none;
        }

        .loading-spinner {
          width: 16px;
          height: 16px;
          border: 2px solid rgba(255, 255, 255, 0.3);
          border-top: 2px solid white;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
      }
    }

    .flag-error {
      color: #ff4444;
      font-size: 0.9rem;
      margin-top: 0.5rem;
    }

    .flag-success {
      color: #00ff88;
      font-size: 0.9rem;
      margin-top: 0.5rem;
      font-weight: 500;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 桌面端模态框适配 */
@media (min-width: 768px) {
  .modal {
    max-width: 700px;

    .modal-header {
      padding: 2.5rem 2.5rem 1.5rem;

      h2 {
        font-size: 1.8rem;
      }
    }

    .modal-content {
      padding: 2.5rem;
    }

    .challenge-info {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}
</style>