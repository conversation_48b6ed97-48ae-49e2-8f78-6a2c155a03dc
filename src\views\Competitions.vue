<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAuthStore } from '../store'

interface Competition {
  id: number
  title: string
  startDate: string
  endDate: string
  format: 'Jeopardy' | 'Attack/Defense' | 'King of the Hill'
  difficulty: string
  teamSize: string
  prize: string
  registered: boolean
  description: string
  status: 'upcoming' | 'ongoing' | 'completed'
}

const authStore = useAuthStore()
const showRegistrationModal = ref(false)
const selectedCompetition = ref<Competition | null>(null)
const teamName = ref('')
const teamMembers = ref<string[]>([''])

const competitions: Competition[] = [
  {
    id: 1,
    title: '2025 全球 CTF 锦标赛',
    startDate: '2025-03-15T10:00:00Z',
    endDate: '2025-03-17T10:00:00Z',
    format: 'Jeopardy',
    difficulty: '高级',
    teamSize: '2-4 人',
    prize: '$5000',
    registered: false,
    description: '参加年度最具声望的 CTF 竞赛。面对各种安全领域的挑战性问题。',
    status: 'upcoming'
  },
  {
    id: 2,
    title: '网络防御联盟',
    startDate: '2025-02-28T15:00:00Z',
    endDate: '2025-02-29T15:00:00Z',
    format: 'Attack/Defense',
    difficulty: '中级',
    teamSize: '3-5 人',
    prize: '$3000',
    registered: false,
    description: '实时攻防竞赛。保护您的基础设施，同时攻击其他队伍。',
    status: 'ongoing'
  }
]

const filterOptions = ref({
  status: 'all',
  format: 'all'
})

const filteredCompetitions = computed(() => {
  return competitions.filter(comp => {
    const statusMatch = filterOptions.value.status === 'all' || comp.status === filterOptions.value.status
    const formatMatch = filterOptions.value.format === 'all' || comp.format === filterOptions.value.format
    return statusMatch && formatMatch
  })
})

const openRegistration = (competition: Competition) => {
  selectedCompetition.value = competition
  showRegistrationModal.value = true
}

const addTeamMember = () => {
  if (teamMembers.value.length < 4) {
    teamMembers.value.push('')
  }
}

const removeTeamMember = (index: number) => {
  if (teamMembers.value.length > 1) {
    teamMembers.value.splice(index, 1)
  }
}

const registerTeam = () => {
  // Here you would implement the team registration logic
  console.log('Registering team:', {
    competitionId: selectedCompetition.value?.id,
    teamName: teamName.value,
    members: teamMembers.value.filter(member => member.trim() !== '')
  })
  showRegistrationModal.value = false
  teamName.value = ''
  teamMembers.value = ['']
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<template>
  <div class="competitions">
    <h1>CTF 竞赛</h1>

    <!-- Filters -->
    <div class="filters">
      <div class="filter-group">
        <label>状态：</label>
        <select v-model="filterOptions.status">
          <option value="all">全部</option>
          <option value="upcoming">即将开始</option>
          <option value="ongoing">进行中</option>
          <option value="completed">已结束</option>
        </select>
      </div>
      <div class="filter-group">
        <label>格式：</label>
        <select v-model="filterOptions.format">
          <option value="all">全部</option>
          <option value="Jeopardy">Jeopardy</option>
          <option value="Attack/Defense">攻防对抗</option>
          <option value="King of the Hill">山丘之王</option>
        </select>
      </div>
    </div>

    <!-- Competition Cards -->
    <div class="competition-grid">
      <div v-for="competition in filteredCompetitions"
           :key="competition.id"
           class="competition-card"
           :class="competition.status">
        <div class="status-badge">{{
          competition.status === 'upcoming' ? '即将开始' :
          competition.status === 'ongoing' ? '进行中' :
          competition.status === 'completed' ? '已结束' : competition.status
        }}</div>
        <h2>{{ competition.title }}</h2>
        <div class="competition-details">
          <div class="detail">
            <span class="label">格式：</span>
            <span class="value">{{ competition.format }}</span>
          </div>
          <div class="detail">
            <span class="label">开始时间：</span>
            <span class="value">{{ formatDate(competition.startDate) }}</span>
          </div>
          <div class="detail">
            <span class="label">结束时间：</span>
            <span class="value">{{ formatDate(competition.endDate) }}</span>
          </div>
          <div class="detail">
            <span class="label">队伍规模：</span>
            <span class="value">{{ competition.teamSize }}</span>
          </div>
          <div class="detail">
            <span class="label">奖金池：</span>
            <span class="value">{{ competition.prize }}</span>
          </div>
        </div>
        <p class="description">{{ competition.description }}</p>
        <button
          @click="openRegistration(competition)"
          :disabled="competition.status === 'completed' || competition.registered"
          class="register-btn"
        >
          {{ competition.registered ? '已报名' : '立即报名' }}
        </button>
      </div>
    </div>

    <!-- Registration Modal -->
    <div v-if="showRegistrationModal" class="modal-overlay">
      <div class="modal">
        <h2>报名参加 {{ selectedCompetition?.title }}</h2>
        <form @submit.prevent="registerTeam">
          <div class="form-group">
            <label>队伍名称</label>
            <input
              v-model="teamName"
              type="text"
              required
              placeholder="请输入队伍名称"
            >
          </div>

          <div class="form-group">
            <label>队伍成员</label>
            <div v-for="(member, index) in teamMembers"
                 :key="index"
                 class="team-member-input">
              <input
                v-model="teamMembers[index]"
                type="text"
                required
                placeholder="请输入成员邮箱"
              >
              <button
                type="button"
                @click="removeTeamMember(index)"
                class="remove-member"
                :disabled="teamMembers.length === 1"
              >
                ×
              </button>
            </div>
            <button
              type="button"
              @click="addTeamMember"
              class="add-member"
              :disabled="teamMembers.length >= 4"
            >
              + 添加队伍成员
            </button>
          </div>

          <div class="modal-actions">
            <button type="submit" class="submit-btn">注册队伍</button>
            <button
              type="button"
              @click="showRegistrationModal = false"
              class="cancel-btn"
            >
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.competitions {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  color: white;
}

h1 {
  text-align: center;
  margin-bottom: 2rem;
  color: #00ff88;
}

.filters {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  justify-content: center;

  .filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    label {
      color: #00ff88;
    }

    select {
      padding: 0.5rem;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 5px;
      color: white;
      cursor: pointer;

      &:focus {
        border-color: #00ff88;
        outline: none;
      }

      option {
        background: #1a1a1a;
      }
    }
  }
}

.competition-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.competition-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 1.5rem;
  position: relative;
  backdrop-filter: blur(10px);
  transition: transform 0.3s;

  &:hover {
    transform: translateY(-5px);
  }

  &.upcoming {
    border: 2px solid #00ff88;
  }

  &.ongoing {
    border: 2px solid #ffd700;
  }

  &.completed {
    border: 2px solid #ff4444;
    opacity: 0.8;
  }

  .status-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    text-transform: uppercase;
    font-weight: bold;

    .upcoming & {
      background: rgba(0, 255, 136, 0.2);
      color: #00ff88;
    }

    .ongoing & {
      background: rgba(255, 215, 0, 0.2);
      color: #ffd700;
    }

    .completed & {
      background: rgba(255, 68, 68, 0.2);
      color: #ff4444;
    }
  }

  h2 {
    color: white;
    margin-bottom: 1rem;
    padding-right: 100px;
  }

  .competition-details {
    margin: 1rem 0;

    .detail {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.5rem;

      .label {
        color: #00ff88;
      }

      .value {
        color: white;
      }
    }
  }

  .description {
    color: rgba(255, 255, 255, 0.8);
    margin: 1rem 0;
  }

  .register-btn {
    width: 100%;
    padding: 0.8rem;
    background: linear-gradient(45deg, #00ff88, #00a3ff);
    border: none;
    border-radius: 5px;
    color: white;
    cursor: pointer;
    transition: transform 0.2s;

    &:hover:not(:disabled) {
      transform: scale(1.02);
    }

    &:disabled {
      background: rgba(255, 255, 255, 0.1);
      cursor: not-allowed;
    }
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: #1a1a1a;
  padding: 2rem;
  border-radius: 10px;
  width: 90%;
  max-width: 500px;

  h2 {
    color: #00ff88;
    margin-bottom: 1.5rem;
  }

  .form-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #00ff88;
    }

    input {
      width: 100%;
      padding: 0.8rem;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 5px;
      color: white;
      margin-bottom: 0.5rem;

      &:focus {
        border-color: #00ff88;
        outline: none;
      }
    }
  }

  .team-member-input {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;

    .remove-member {
      background: rgba(255, 68, 68, 0.2);
      color: #ff4444;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      padding: 0 1rem;

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  .add-member {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
    border: none;
    padding: 0.5rem;
    border-radius: 5px;
    cursor: pointer;
    width: 100%;
    margin-top: 0.5rem;

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .modal-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;

    button {
      flex: 1;
      padding: 0.8rem;
      border-radius: 5px;
      cursor: pointer;
      transition: transform 0.2s;

      &:hover {
        transform: scale(1.02);
      }
    }

    .submit-btn {
      background: #00ff88;
      color: black;
      border: none;
    }

    .cancel-btn {
      background: transparent;
      border: 2px solid #ff4444;
      color: #ff4444;
    }
  }
}
</style>