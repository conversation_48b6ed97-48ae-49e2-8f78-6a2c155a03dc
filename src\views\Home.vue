<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuthStore, useSimulationStore, useReportsStore } from '../store'

const authStore = useAuthStore()
const simulationStore = useSimulationStore()
const reportsStore = useReportsStore()

// 平台统计数据
const platformStats = ref({
  totalUsers: 1247,
  activeSessions: 23,
  completedSessions: 8956,
  avgScore: 78.5
})

// 实时活动数据
const recentActivities = ref([
  { user: 'Alex Chen', action: '完成Web安全推演', score: 92, time: '2分钟前' },
  { user: '<PERSON>', action: '发现SQL注入漏洞', score: 85, time: '5分钟前' },
  { user: '<PERSON>', action: '成功防御APT攻击', score: 88, time: '8分钟前' },
  { user: 'Lisa <PERSON>', action: '完成网络渗透测试', score: 94, time: '12分钟前' }
])

// 热门场景
const popularScenarios = ref([
  { name: 'Web应用渗透测试', participants: 156, difficulty: '中等' },
  { name: '企业网络防护', participants: 89, difficulty: '高级' },
  { name: 'IoT设备安全', participants: 67, difficulty: '专家' },
  { name: '云安全评估', participants: 134, difficulty: '中等' }
])

onMounted(() => {
  // 模拟实时数据更新
  setInterval(() => {
    // 随机更新活跃会话数
    platformStats.value.activeSessions = Math.floor(Math.random() * 50) + 10

    // 随机更新平均分
    platformStats.value.avgScore = Math.floor(Math.random() * 20) + 70
  }, 5000)
})
</script>

<template>
  <div class="home">
    <!-- 动态背景 -->
    <div class="animated-bg">
      <div class="bg-element" v-for="i in 20" :key="i"></div>
    </div>

    <div class="content">
      <h1>AI 驱动的动态攻防推演平台</h1>
      <p class="subtitle">智能化、自动化的攻防推演，超越传统CTF的全新体验</p>

      <!-- 平台统计 -->
      <div class="platform-stats">
        <div class="stat-item">
          <div class="stat-number">{{ platformStats.totalUsers.toLocaleString() }}</div>
          <div class="stat-label">注册用户</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ platformStats.activeSessions }}</div>
          <div class="stat-label">活跃推演</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ platformStats.completedSessions.toLocaleString() }}</div>
          <div class="stat-label">完成会话</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ platformStats.avgScore }}%</div>
          <div class="stat-label">平均评分</div>
        </div>
      </div>

      <div class="features">
        <div class="feature">
          <h3>🎯 动态场景生成</h3>
          <p>AI自动生成真实业务场景，支持网络拓扑可视化编辑和自定义配置</p>
        </div>

        <div class="feature">
          <h3>⚡ 实时攻防推演</h3>
          <p>观看AI攻击与防御的实时对抗，支持时间轴回放和攻击路径可视化</p>
        </div>

        <div class="feature">
          <h3>🤖 AI Agent控制</h3>
          <p>调整攻击和防御AI的策略强度，监控AI决策过程和行为日志</p>
        </div>

        <div class="feature">
          <h3>📊 智能评估报告</h3>
          <p>自动生成多维度评估报告，提供技能画像和量化分析结果</p>
        </div>
      </div>

      <div class="mode-selection">
        <h2>选择演练模式</h2>
        <div class="mode-cards">
          <router-link to="/scenario" class="mode-card">
            <div class="mode-icon">🎮</div>
            <h3>自由演练</h3>
            <p>自定义场景和AI参数，探索无限可能</p>
          </router-link>

          <router-link to="/simulation" class="mode-card">
            <div class="mode-icon">🏆</div>
            <h3>挑战模式</h3>
            <p>系统生成特定目标，AI攻防对抗</p>
          </router-link>

          <router-link to="/control" class="mode-card">
            <div class="mode-icon">⚔️</div>
            <h3>比赛模式</h3>
            <p>专业渗透测试竞赛入口</p>
          </router-link>
        </div>
      </div>

      <!-- 实时活动和热门场景 -->
      <div class="live-data">
        <div class="live-section">
          <h3>🔥 实时活动</h3>
          <div class="activity-list">
            <div v-for="activity in recentActivities" :key="activity.user" class="activity-item">
              <div class="activity-user">{{ activity.user }}</div>
              <div class="activity-action">{{ activity.action }}</div>
              <div class="activity-score">{{ activity.score }}分</div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
          </div>
        </div>

        <div class="live-section">
          <h3>🎯 热门场景</h3>
          <div class="scenario-list">
            <div v-for="scenario in popularScenarios" :key="scenario.name" class="scenario-item">
              <div class="scenario-name">{{ scenario.name }}</div>
              <div class="scenario-meta">
                <span class="participants">{{ scenario.participants }}人参与</span>
                <span class="difficulty" :class="scenario.difficulty">{{ scenario.difficulty }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="cta-buttons">
        <router-link to="/scenario" class="btn primary">开始推演</router-link>
        <router-link to="/auth" class="btn secondary" v-if="!authStore.isAuthenticated">立即注册</router-link>
        <router-link to="/profile" class="btn secondary" v-else>个人中心</router-link>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.home {
  min-height: 100vh;
  position: relative;
  color: white;
  overflow: hidden;
  padding-top: 70px; /* 为导航栏留出空间 */
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
}

.animated-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;

  .bg-element {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(0, 255, 136, 0.3);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;

    &:nth-child(odd) {
      background: rgba(0, 163, 255, 0.3);
      animation-duration: 8s;
    }

    &:nth-child(3n) {
      background: rgba(255, 255, 255, 0.2);
      animation-duration: 10s;
    }

    @for $i from 1 through 20 {
      &:nth-child(#{$i}) {
        left: #{random(100)}%;
        top: #{random(100)}%;
        animation-delay: #{random(60)}s;
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.8;
  }
}

.content {
  position: relative;
  z-index: 2;
  padding: 2rem;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: calc(100vh - 70px);
}

h1 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #00ff88, #00a3ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  font-size: 1.5rem;
  margin-bottom: 3rem;
  opacity: 0.9;
}

.platform-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin: 3rem 0;

  .stat-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 255, 136, 0.2);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      border-color: #00ff88;
      box-shadow: 0 10px 30px rgba(0, 255, 136, 0.2);
    }

    .stat-number {
      font-size: 2.5rem;
      font-weight: bold;
      background: linear-gradient(45deg, #00ff88, #00a3ff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-bottom: 0.5rem;
    }

    .stat-label {
      color: rgba(255, 255, 255, 0.8);
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
  }
}

.live-data {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin: 4rem 0;

  .live-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 255, 136, 0.2);

    h3 {
      color: #00ff88;
      margin-bottom: 1.5rem;
      font-size: 1.3rem;
      text-align: center;
    }
  }

  .activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .activity-item {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      padding: 1rem;
      display: grid;
      grid-template-columns: 1fr 2fr auto auto;
      gap: 1rem;
      align-items: center;
      font-size: 0.9rem;

      .activity-user {
        font-weight: bold;
        color: #00ff88;
      }

      .activity-action {
        color: rgba(255, 255, 255, 0.9);
      }

      .activity-score {
        color: #00a3ff;
        font-weight: bold;
      }

      .activity-time {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.8rem;
      }
    }
  }

  .scenario-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .scenario-item {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      padding: 1rem;

      .scenario-name {
        font-weight: bold;
        color: white;
        margin-bottom: 0.5rem;
      }

      .scenario-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.9rem;

        .participants {
          color: rgba(255, 255, 255, 0.7);
        }

        .difficulty {
          padding: 0.2rem 0.8rem;
          border-radius: 15px;
          font-size: 0.8rem;
          font-weight: bold;

          &.简单 {
            background: rgba(0, 255, 136, 0.2);
            color: #00ff88;
          }

          &.中等 {
            background: rgba(255, 165, 0, 0.2);
            color: #ffa500;
          }

          &.困难, &.高级 {
            background: rgba(255, 68, 68, 0.2);
            color: #ff4444;
          }

          &.专家 {
            background: rgba(139, 92, 246, 0.2);
            color: #8b5cf6;
          }
        }
      }
    }
  }
}

/* 移动端适配 */
@media (max-width: 767px) {
  .platform-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin: 2rem 0;

    .stat-item {
      padding: 1rem;

      .stat-number {
        font-size: 2rem;
      }

      .stat-label {
        font-size: 0.8rem;
      }
    }
  }

  .live-data {
    grid-template-columns: 1fr;
    gap: 2rem;
    margin: 3rem 0;

    .live-section {
      padding: 1.5rem;
    }

    .activity-item {
      grid-template-columns: 1fr;
      gap: 0.5rem;
      text-align: center;
    }
  }

  .mode-selection .mode-cards {
    grid-template-columns: 1fr;
  }
}

/* 桌面端适配 */
@media (min-width: 768px) {
  .home {
    padding-top: 80px;
  }

  .content {
    padding: 3rem;
    max-width: 1400px;
    min-height: calc(100vh - 80px);
  }

  h1 {
    font-size: 4rem;
    margin-bottom: 1.2rem;
  }

  .subtitle {
    font-size: 1.8rem;
    margin-bottom: 3rem;
  }

  .platform-stats {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;

    .stat-item {
      padding: 2rem;

      .stat-number {
        font-size: 3rem;
      }
    }
  }
}

@media (min-width: 1200px) {
  .content {
    padding: 4rem;
    max-width: 1600px;
  }

  h1 {
    font-size: 4.5rem;
  }

  .subtitle {
    font-size: 2rem;
  }
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin: 4rem 0;
}

.mode-selection {
  margin: 5rem 0;
  text-align: center;

  h2 {
    color: #00ff88;
    margin-bottom: 3rem;
    font-size: 2.5rem;
  }

  .mode-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
  }

  .mode-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 255, 136, 0.2);

    &:hover {
      transform: translateY(-10px);
      box-shadow: 0 20px 40px rgba(0, 255, 136, 0.3);
      border-color: #00ff88;
    }

    .mode-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    h3 {
      color: #00ff88;
      margin-bottom: 1rem;
      font-size: 1.5rem;
    }

    p {
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.6;
    }
  }
}

.feature {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  backdrop-filter: blur(10px);
  transition: transform 0.3s;

  &:hover {
    transform: translateY(-5px);
  }

  h3 {
    color: #00ff88;
    margin-bottom: 1rem;
    font-size: 1.3rem;
  }

  p {
    font-size: 1rem;
    line-height: 1.6;
  }
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 3rem;
  flex-wrap: wrap;
}

.btn {
  padding: 1rem 2rem;
  border-radius: 30px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;
  font-size: 1rem;
  text-decoration: none;
  display: inline-block;

  &.primary {
    background: linear-gradient(45deg, #00ff88, #00a3ff);
    color: white;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
    }
  }

  &.secondary {
    border: 2px solid #00ff88;
    color: #00ff88;
    background: transparent;

    &:hover {
      background: #00ff88;
      color: black;
    }
  }
}

/* 桌面端特性区域适配 */
@media (min-width: 768px) {
  .features {
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
    margin: 4rem 0;
  }

  .feature {
    padding: 2.5rem;

    h3 {
      font-size: 1.4rem;
      margin-bottom: 1.2rem;
    }

    p {
      font-size: 1rem;
    }
  }

  .cta-buttons {
    gap: 1.5rem;
    margin-top: 3rem;
  }

  .btn {
    padding: 1rem 2.5rem;
    font-size: 1rem;
  }
}

@media (min-width: 1200px) {
  .features {
    gap: 3rem;
    margin: 5rem 0;
  }

  .feature {
    padding: 3rem;

    h3 {
      font-size: 1.5rem;
    }

    p {
      font-size: 1.1rem;
    }
  }

  .btn {
    padding: 1.2rem 3rem;
    font-size: 1.1rem;
  }
}
</style>