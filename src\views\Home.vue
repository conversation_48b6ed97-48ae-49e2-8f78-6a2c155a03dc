<script setup lang="ts">
import { onMounted } from 'vue'
import Particles from '@tsparticles/vue3'
import { loadFull } from "tsparticles"

const particlesOptions = {
  particles: {
    number: {
      value: 80,
      density: {
        enable: true,
        value_area: 800
      }
    },
    color: {
      value: "#ffffff"
    },
    links: {
      enable: true,
      color: "#ffffff",
      opacity: 0.4
    },
    move: {
      enable: true,
      speed: 2
    }
  }
}

const particlesInit = async (engine: any) => {
  await loadFull(engine)
}
</script>

<template>
  <div class="home">
    <Particles
      id="tsparticles"
      :options="particlesOptions"
      :init="particlesInit"
      class="particles"
    />

    <div class="content">
      <h1>欢迎来到 CTF 擂台平台</h1>
      <p class="subtitle">通过挑战和竞赛提升您的网络安全技能</p>

      <div class="features">
        <div class="feature">
          <h3>练习挑战</h3>
          <p>访问各种类别的 CTF 挑战题目，提升技能水平</p>
        </div>

        <div class="feature">
          <h3>实时竞赛</h3>
          <p>参与实时 CTF 竞赛，测试您的技能并与他人竞技</p>
        </div>

        <div class="feature">
          <h3>进度追踪</h3>
          <p>监控您的学习进度，不断提升网络安全专业技能</p>
        </div>
      </div>

      <div class="cta-buttons">
        <router-link to="/challenges" class="btn primary">开始练习</router-link>
        <router-link to="/auth" class="btn secondary">立即注册</router-link>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.home {
  min-height: 100vh;
  position: relative;
  color: white;
  overflow: hidden;
}

.particles {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.content {
  position: relative;
  z-index: 2;
  padding: 2rem;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #00ff88, #00a3ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  font-size: 1.5rem;
  margin-bottom: 3rem;
  opacity: 0.9;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin: 4rem 0;
}

.feature {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  backdrop-filter: blur(10px);
  transition: transform 0.3s;

  &:hover {
    transform: translateY(-5px);
  }

  h3 {
    color: #00ff88;
    margin-bottom: 1rem;
  }
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 3rem;
}

.btn {
  padding: 1rem 2rem;
  border-radius: 30px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;

  &.primary {
    background: linear-gradient(45deg, #00ff88, #00a3ff);
    color: white;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
    }
  }

  &.secondary {
    border: 2px solid #00ff88;
    color: #00ff88;

    &:hover {
      background: #00ff88;
      color: black;
    }
  }
}
</style>