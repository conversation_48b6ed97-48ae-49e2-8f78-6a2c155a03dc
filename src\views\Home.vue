<script setup lang="ts">
import { onMounted } from 'vue'
import Particles from '@tsparticles/vue3'
import { loadFull } from "tsparticles"

const particlesOptions = {
  particles: {
    number: {
      value: 80,
      density: {
        enable: true,
        value_area: 800
      }
    },
    color: {
      value: "#ffffff"
    },
    links: {
      enable: true,
      color: "#ffffff",
      opacity: 0.4
    },
    move: {
      enable: true,
      speed: 2
    }
  }
}

const particlesInit = async (engine: any) => {
  await loadFull(engine)
}
</script>

<template>
  <div class="home">
    <Particles
      id="tsparticles"
      :options="particlesOptions"
      :init="particlesInit"
      class="particles"
    />

    <div class="content">
      <h1>AI 驱动的动态攻防推演平台</h1>
      <p class="subtitle">智能化、自动化的攻防推演，超越传统CTF的全新体验</p>

      <div class="features">
        <div class="feature">
          <h3>🎯 动态场景生成</h3>
          <p>AI自动生成真实业务场景，支持网络拓扑可视化编辑和自定义配置</p>
        </div>

        <div class="feature">
          <h3>⚡ 实时攻防推演</h3>
          <p>观看AI攻击与防御的实时对抗，支持时间轴回放和攻击路径可视化</p>
        </div>

        <div class="feature">
          <h3>🤖 AI Agent控制</h3>
          <p>调整攻击和防御AI的策略强度，监控AI决策过程和行为日志</p>
        </div>

        <div class="feature">
          <h3>📊 智能评估报告</h3>
          <p>自动生成多维度评估报告，提供技能画像和量化分析结果</p>
        </div>
      </div>

      <div class="mode-selection">
        <h2>选择演练模式</h2>
        <div class="mode-cards">
          <router-link to="/scenario" class="mode-card">
            <div class="mode-icon">🎮</div>
            <h3>自由演练</h3>
            <p>自定义场景和AI参数，探索无限可能</p>
          </router-link>

          <router-link to="/simulation" class="mode-card">
            <div class="mode-icon">🏆</div>
            <h3>挑战模式</h3>
            <p>系统生成特定目标，AI攻防对抗</p>
          </router-link>

          <router-link to="/control" class="mode-card">
            <div class="mode-icon">⚔️</div>
            <h3>比赛模式</h3>
            <p>专业渗透测试竞赛入口</p>
          </router-link>
        </div>
      </div>

      <div class="cta-buttons">
        <router-link to="/scenario" class="btn primary">开始推演</router-link>
        <router-link to="/auth" class="btn secondary">立即注册</router-link>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.home {
  min-height: 100vh;
  position: relative;
  color: white;
  overflow: hidden;
  padding-top: 70px; /* 为导航栏留出空间 */
}

.particles {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.content {
  position: relative;
  z-index: 2;
  padding: 2rem;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: calc(100vh - 70px);
}

h1 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #00ff88, #00a3ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  font-size: 1.5rem;
  margin-bottom: 3rem;
  opacity: 0.9;
}

/* 桌面端适配 */
@media (min-width: 768px) {
  .home {
    padding-top: 80px;
  }

  .content {
    padding: 3rem;
    max-width: 1400px;
    min-height: calc(100vh - 80px);
  }

  h1 {
    font-size: 4rem;
    margin-bottom: 1.2rem;
  }

  .subtitle {
    font-size: 1.8rem;
    margin-bottom: 3rem;
  }
}

@media (min-width: 1200px) {
  .content {
    padding: 4rem;
    max-width: 1600px;
  }

  h1 {
    font-size: 4.5rem;
  }

  .subtitle {
    font-size: 2rem;
  }
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin: 4rem 0;
}

.mode-selection {
  margin: 5rem 0;
  text-align: center;

  h2 {
    color: #00ff88;
    margin-bottom: 3rem;
    font-size: 2.5rem;
  }

  .mode-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
  }

  .mode-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 255, 136, 0.2);

    &:hover {
      transform: translateY(-10px);
      box-shadow: 0 20px 40px rgba(0, 255, 136, 0.3);
      border-color: #00ff88;
    }

    .mode-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    h3 {
      color: #00ff88;
      margin-bottom: 1rem;
      font-size: 1.5rem;
    }

    p {
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.6;
    }
  }
}

.feature {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  backdrop-filter: blur(10px);
  transition: transform 0.3s;

  &:hover {
    transform: translateY(-5px);
  }

  h3 {
    color: #00ff88;
    margin-bottom: 1rem;
    font-size: 1.3rem;
  }

  p {
    font-size: 1rem;
    line-height: 1.6;
  }
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 3rem;
  flex-wrap: wrap;
}

.btn {
  padding: 1rem 2rem;
  border-radius: 30px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s;
  font-size: 1rem;
  text-decoration: none;
  display: inline-block;

  &.primary {
    background: linear-gradient(45deg, #00ff88, #00a3ff);
    color: white;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
    }
  }

  &.secondary {
    border: 2px solid #00ff88;
    color: #00ff88;
    background: transparent;

    &:hover {
      background: #00ff88;
      color: black;
    }
  }
}

/* 桌面端特性区域适配 */
@media (min-width: 768px) {
  .features {
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
    margin: 4rem 0;
  }

  .feature {
    padding: 2.5rem;

    h3 {
      font-size: 1.4rem;
      margin-bottom: 1.2rem;
    }

    p {
      font-size: 1rem;
    }
  }

  .cta-buttons {
    gap: 1.5rem;
    margin-top: 3rem;
  }

  .btn {
    padding: 1rem 2.5rem;
    font-size: 1rem;
  }
}

@media (min-width: 1200px) {
  .features {
    gap: 3rem;
    margin: 5rem 0;
  }

  .feature {
    padding: 3rem;

    h3 {
      font-size: 1.5rem;
    }

    p {
      font-size: 1.1rem;
    }
  }

  .btn {
    padding: 1.2rem 3rem;
    font-size: 1.1rem;
  }
}
</style>