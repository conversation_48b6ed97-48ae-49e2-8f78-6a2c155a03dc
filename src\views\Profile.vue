<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore, useChallengeStore } from '../store'

const authStore = useAuthStore()
const challengeStore = useChallengeStore()

// 计算用户统计数据
const stats = computed(() => {
  const solvedChallenges = challengeStore.solvedChallenges.length
  const totalPoints = challengeStore.totalPoints
  const totalChallenges = challengeStore.challenges.length
  const completionRate = totalChallenges > 0 ? Math.round((solvedChallenges / totalChallenges) * 100) : 0

  return {
    solvedChallenges,
    totalPoints,
    rank: 42, // 模拟排名
    completionRate
  }
})

// 最近活动数据
const recentActivity = computed(() => {
  return challengeStore.solvedChallenges
    .filter(challenge => challenge.solvedAt)
    .sort((a, b) => new Date(b.solvedAt!).getTime() - new Date(a.solvedAt!).getTime())
    .slice(0, 5)
    .map(challenge => ({
      id: challenge.id,
      type: 'challenge',
      name: challenge.title,
      points: challenge.points,
      date: challenge.solvedAt!,
      category: challenge.category
    }))
})

// 分类统计
const categoryStats = computed(() => challengeStore.challengesByCategory)

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化相对时间
const formatRelativeTime = (dateString: string) => {
  const now = new Date()
  const date = new Date(dateString)
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return '刚刚'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} 分钟前`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} 小时前`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} 天前`

  return formatDate(dateString)
}

// 获取用户加入时间
const joinDate = computed(() => {
  if (authStore.user?.joinDate) {
    return formatDate(authStore.user.joinDate)
  }
  return '未知'
})

onMounted(() => {
  // 确保挑战数据已初始化
  if (challengeStore.challenges.length === 0) {
    challengeStore.initChallenges()
  }
})
</script>

<template>
  <div class="profile">
    <div class="profile-header">
      <div class="user-info">
        <div class="avatar">
          {{ authStore.userName.charAt(0).toUpperCase() }}
        </div>
        <div class="user-details">
          <h1>{{ authStore.userName }}</h1>
          <p class="email">{{ authStore.userEmail }}</p>
          <p class="join-date">加入时间：{{ joinDate }}</p>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">🏆</div>
        <h3>已解决挑战</h3>
        <p class="stat-value">{{ stats.solvedChallenges }}</p>
        <p class="stat-total">/ {{ challengeStore.challenges.length }}</p>
      </div>
      <div class="stat-card">
        <div class="stat-icon">⭐</div>
        <h3>总积分</h3>
        <p class="stat-value">{{ stats.totalPoints }}</p>
        <p class="stat-unit">分</p>
      </div>
      <div class="stat-card">
        <div class="stat-icon">📊</div>
        <h3>全球排名</h3>
        <p class="stat-value">#{{ stats.rank }}</p>
        <p class="stat-trend">↑ 5</p>
      </div>
      <div class="stat-card">
        <div class="stat-icon">📈</div>
        <h3>完成率</h3>
        <p class="stat-value">{{ stats.completionRate }}%</p>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: stats.completionRate + '%' }"></div>
        </div>
      </div>
    </div>

    <!-- 分类统计 -->
    <div class="category-stats">
      <h2>分类统计</h2>
      <div class="category-grid">
        <div v-for="category in categoryStats"
             :key="category.name"
             class="category-card">
          <h3>{{ category.name }}</h3>
          <div class="category-progress">
            <span class="solved">{{ category.solved }}</span>
            <span class="total">/ {{ category.challenges.length }}</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill"
                 :style="{ width: category.challenges.length > 0 ? (category.solved / category.challenges.length * 100) + '%' : '0%' }">
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity">
      <h2>最近活动</h2>
      <div v-if="recentActivity.length === 0" class="no-activity">
        <p>还没有完成任何挑战，快去<router-link to="/challenges">挑战页面</router-link>开始吧！</p>
      </div>
      <div v-else class="activity-list">
        <div v-for="activity in recentActivity"
             :key="activity.id"
             class="activity-item">
          <div class="activity-icon challenge">
            <span>🎯</span>
          </div>
          <div class="activity-details">
            <h4>{{ activity.name }}</h4>
            <p class="activity-meta">
              <span class="category">{{ activity.category }}</span>
              <span class="points">+{{ activity.points }} 分</span>
              <span class="date">{{ formatRelativeTime(activity.date) }}</span>
            </p>
          </div>
          <div class="activity-badge">
            ✓
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.profile {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  color: white;
  padding-top: calc(70px + 2rem); /* 为导航栏留出空间 */
  min-height: 100vh;
}

.profile-header {
  margin-bottom: 3rem;

  .user-info {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);

    .avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: linear-gradient(45deg, #00ff88, #00a3ff);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      font-weight: bold;
      color: white;
      flex-shrink: 0;
    }

    .user-details {
      flex: 1;

      h1 {
        color: #00ff88;
        margin-bottom: 0.5rem;
        font-size: 2.5rem;
      }

      .email {
        opacity: 0.7;
        font-size: 1.1rem;
        margin-bottom: 0.3rem;
      }

      .join-date {
        opacity: 0.6;
        font-size: 0.9rem;
      }
    }
  }
}

/* 桌面端适配 */
@media (min-width: 768px) {
  .profile {
    padding: 4rem;
    max-width: 1400px;
    padding-top: calc(80px + 4rem);
  }

  .profile-header {
    margin-bottom: 4rem;

    h1 {
      font-size: 3.5rem;
      margin-bottom: 1rem;
    }

    .email {
      font-size: 1.3rem;
    }
  }
}

@media (min-width: 1200px) {
  .profile {
    padding: 6rem;
    max-width: 1600px;
  }

  .profile-header {
    margin-bottom: 5rem;

    h1 {
      font-size: 4rem;
    }

    .email {
      font-size: 1.5rem;
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  border-radius: 15px;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: transform 0.3s, box-shadow 0.3s;
  border: 1px solid rgba(0, 255, 136, 0.2);

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 255, 136, 0.2);
  }

  .stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
  }

  h3 {
    color: #00ff88;
    margin-bottom: 1rem;
    font-size: 1.1rem;
  }

  .stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: white;
  }

  .stat-total, .stat-unit {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
    margin-top: 0.3rem;
  }

  .stat-trend {
    color: #00ff88;
    font-size: 0.9rem;
    margin-top: 0.3rem;
  }

  .progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    margin-top: 1rem;
    overflow: hidden;

    .progress-fill {
      height: 100%;
      background: linear-gradient(45deg, #00ff88, #00a3ff);
      border-radius: 3px;
      transition: width 0.8s ease;
    }
  }
}

// 分类统计样式
.category-stats {
  margin-bottom: 3rem;

  h2 {
    color: #00ff88;
    margin-bottom: 2rem;
    font-size: 2rem;
  }

  .category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }

  .category-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 255, 136, 0.1);
    transition: transform 0.3s;

    &:hover {
      transform: translateY(-3px);
    }

    h3 {
      color: #00ff88;
      margin-bottom: 1rem;
      font-size: 1rem;
    }

    .category-progress {
      display: flex;
      align-items: center;
      gap: 0.3rem;
      margin-bottom: 0.8rem;

      .solved {
        color: #00ff88;
        font-weight: bold;
        font-size: 1.2rem;
      }

      .total {
        color: rgba(255, 255, 255, 0.6);
        font-size: 1rem;
      }
    }

    .progress-bar {
      width: 100%;
      height: 4px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 2px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(45deg, #00ff88, #00a3ff);
        border-radius: 2px;
        transition: width 0.6s ease;
      }
    }
  }
}

.recent-activity {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 255, 136, 0.2);

  h2 {
    color: #00ff88;
    margin-bottom: 1.5rem;
    font-size: 2rem;
  }

  .no-activity {
    text-align: center;
    padding: 3rem;
    color: rgba(255, 255, 255, 0.6);

    a {
      color: #00ff88;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

/* 桌面端统计和活动适配 */
@media (min-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 3rem;
    margin-bottom: 4rem;
  }

  .stat-card {
    padding: 2rem;

    h3 {
      font-size: 1.3rem;
      margin-bottom: 1.5rem;
    }

    .stat-value {
      font-size: 2.5rem;
    }
  }

  .recent-activity {
    padding: 3rem;

    h2 {
      font-size: 2.5rem;
      margin-bottom: 2rem;
    }
  }
}

@media (min-width: 1200px) {
  .stats-grid {
    gap: 4rem;
    margin-bottom: 5rem;
  }

  .stat-card {
    padding: 3rem;

    h3 {
      font-size: 1.5rem;
    }

    .stat-value {
      font-size: 3rem;
    }
  }

  .recent-activity {
    padding: 4rem;

    h2 {
      font-size: 3rem;
    }
  }
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.2rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  transition: transform 0.3s, box-shadow 0.3s;
  border: 1px solid rgba(255, 255, 255, 0.1);

  &:hover {
    transform: translateX(10px);
    box-shadow: 0 5px 20px rgba(0, 255, 136, 0.1);
  }

  .activity-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 255, 136, 0.2);
    border: 2px solid rgba(0, 255, 136, 0.3);

    span {
      font-size: 1.2rem;
    }
  }

  .activity-details {
    flex: 1;

    h4 {
      margin-bottom: 0.5rem;
      font-size: 1.1rem;
      color: white;
    }

    .activity-meta {
      display: flex;
      gap: 1rem;
      font-size: 0.9rem;
      opacity: 0.8;
      flex-wrap: wrap;

      .category {
        background: rgba(0, 255, 136, 0.2);
        color: #00ff88;
        padding: 0.2rem 0.5rem;
        border-radius: 10px;
        font-size: 0.8rem;
      }

      .points {
        color: #00ff88;
        font-weight: 500;
      }

      .date {
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }

  .activity-badge {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #00ff88;
    color: black;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1rem;
  }
}

/* 桌面端活动列表适配 */
@media (min-width: 768px) {
  .activity-list {
    gap: 1.5rem;
  }

  .activity-item {
    padding: 1.5rem;
    gap: 1.5rem;

    .activity-icon {
      width: 50px;
      height: 50px;
    }

    .activity-details {
      h4 {
        font-size: 1.3rem;
        margin-bottom: 0.8rem;
      }

      .activity-meta {
        font-size: 1rem;
        gap: 1.5rem;
      }
    }
  }
}

@media (min-width: 1200px) {
  .activity-list {
    gap: 2rem;
  }

  .activity-item {
    padding: 2rem;
    gap: 2rem;

    .activity-icon {
      width: 60px;
      height: 60px;
    }

    .activity-details {
      h4 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
      }

      .activity-meta {
        font-size: 1.1rem;
        gap: 2rem;
      }
    }
  }
}
</style>