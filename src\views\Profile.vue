<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuthStore } from '../store'

const authStore = useAuthStore()

interface UserStats {
  solvedChallenges: number
  totalPoints: number
  rank: number
  completionRate: number
}

const stats = ref<UserStats>({
  solvedChallenges: 15,
  totalPoints: 2500,
  rank: 42,
  completionRate: 75
})

const recentActivity = ref([
  {
    id: 1,
    type: 'challenge',
    name: 'SQL 注入基础',
    points: 100,
    date: '2024-02-20T15:30:00Z'
  },
  {
    id: 2,
    type: 'competition',
    name: '全球 CTF 锦标赛',
    points: 500,
    date: '2024-02-18T10:00:00Z'
  }
])

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<template>
  <div class="profile">
    <div class="profile-header">
      <div class="user-info">
        <h1>{{ authStore.user?.name }}</h1>
        <p class="email">{{ authStore.user?.email }}</p>
      </div>
    </div>

    <div class="stats-grid">
      <div class="stat-card">
        <h3>已解决挑战</h3>
        <p class="stat-value">{{ stats.solvedChallenges }}</p>
      </div>
      <div class="stat-card">
        <h3>总积分</h3>
        <p class="stat-value">{{ stats.totalPoints }}</p>
      </div>
      <div class="stat-card">
        <h3>全球排名</h3>
        <p class="stat-value">#{{ stats.rank }}</p>
      </div>
      <div class="stat-card">
        <h3>完成率</h3>
        <p class="stat-value">{{ stats.completionRate }}%</p>
      </div>
    </div>

    <div class="recent-activity">
      <h2>最近活动</h2>
      <div class="activity-list">
        <div v-for="activity in recentActivity"
             :key="activity.id"
             class="activity-item">
          <div class="activity-icon" :class="activity.type"></div>
          <div class="activity-details">
            <h4>{{ activity.name }}</h4>
            <p class="activity-meta">
              <span class="points">+{{ activity.points }} 分</span>
              <span class="date">{{ formatDate(activity.date) }}</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.profile {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  color: white;
  padding-top: calc(70px + 2rem); /* 为导航栏留出空间 */
  min-height: 100vh;
}

.profile-header {
  margin-bottom: 3rem;
  text-align: center;

  h1 {
    color: #00ff88;
    margin-bottom: 0.5rem;
    font-size: 2.5rem;
  }

  .email {
    opacity: 0.7;
    font-size: 1.1rem;
  }
}

/* 桌面端适配 */
@media (min-width: 768px) {
  .profile {
    padding: 4rem;
    max-width: 1400px;
    padding-top: calc(80px + 4rem);
  }

  .profile-header {
    margin-bottom: 4rem;

    h1 {
      font-size: 3.5rem;
      margin-bottom: 1rem;
    }

    .email {
      font-size: 1.3rem;
    }
  }
}

@media (min-width: 1200px) {
  .profile {
    padding: 6rem;
    max-width: 1600px;
  }

  .profile-header {
    margin-bottom: 5rem;

    h1 {
      font-size: 4rem;
    }

    .email {
      font-size: 1.5rem;
    }
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: transform 0.3s;

  &:hover {
    transform: translateY(-5px);
  }

  h3 {
    color: #00ff88;
    margin-bottom: 1rem;
    font-size: 1.1rem;
  }

  .stat-value {
    font-size: 2rem;
    font-weight: bold;
  }
}

.recent-activity {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 10px;
  backdrop-filter: blur(10px);

  h2 {
    color: #00ff88;
    margin-bottom: 1.5rem;
    font-size: 2rem;
  }
}

/* 桌面端统计和活动适配 */
@media (min-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 3rem;
    margin-bottom: 4rem;
  }

  .stat-card {
    padding: 2rem;

    h3 {
      font-size: 1.3rem;
      margin-bottom: 1.5rem;
    }

    .stat-value {
      font-size: 2.5rem;
    }
  }

  .recent-activity {
    padding: 3rem;

    h2 {
      font-size: 2.5rem;
      margin-bottom: 2rem;
    }
  }
}

@media (min-width: 1200px) {
  .stats-grid {
    gap: 4rem;
    margin-bottom: 5rem;
  }

  .stat-card {
    padding: 3rem;

    h3 {
      font-size: 1.5rem;
    }

    .stat-value {
      font-size: 3rem;
    }
  }

  .recent-activity {
    padding: 4rem;

    h2 {
      font-size: 3rem;
    }
  }
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  transition: transform 0.3s;

  &:hover {
    transform: translateX(10px);
  }

  .activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    &.challenge {
      background: rgba(0, 255, 136, 0.2);
    }

    &.competition {
      background: rgba(255, 215, 0, 0.2);
    }
  }

  .activity-details {
    flex: 1;

    h4 {
      margin-bottom: 0.5rem;
      font-size: 1.1rem;
    }

    .activity-meta {
      display: flex;
      gap: 1rem;
      font-size: 0.9rem;
      opacity: 0.7;

      .points {
        color: #00ff88;
        font-weight: 500;
      }
    }
  }
}

/* 桌面端活动列表适配 */
@media (min-width: 768px) {
  .activity-list {
    gap: 1.5rem;
  }

  .activity-item {
    padding: 1.5rem;
    gap: 1.5rem;

    .activity-icon {
      width: 50px;
      height: 50px;
    }

    .activity-details {
      h4 {
        font-size: 1.3rem;
        margin-bottom: 0.8rem;
      }

      .activity-meta {
        font-size: 1rem;
        gap: 1.5rem;
      }
    }
  }
}

@media (min-width: 1200px) {
  .activity-list {
    gap: 2rem;
  }

  .activity-item {
    padding: 2rem;
    gap: 2rem;

    .activity-icon {
      width: 60px;
      height: 60px;
    }

    .activity-details {
      h4 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
      }

      .activity-meta {
        font-size: 1.1rem;
        gap: 2rem;
      }
    }
  }
}
</style>