<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuthStore } from '../store'

const authStore = useAuthStore()

interface UserStats {
  solvedChallenges: number
  totalPoints: number
  rank: number
  completionRate: number
}

const stats = ref<UserStats>({
  solvedChallenges: 15,
  totalPoints: 2500,
  rank: 42,
  completionRate: 75
})

const recentActivity = ref([
  {
    id: 1,
    type: 'challenge',
    name: 'SQL Injection Basics',
    points: 100,
    date: '2024-02-20T15:30:00Z'
  },
  {
    id: 2,
    type: 'competition',
    name: 'Global CTF Championship',
    points: 500,
    date: '2024-02-18T10:00:00Z'
  }
])

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<template>
  <div class="profile">
    <div class="profile-header">
      <div class="user-info">
        <h1>{{ authStore.user?.name }}</h1>
        <p class="email">{{ authStore.user?.email }}</p>
      </div>
    </div>

    <div class="stats-grid">
      <div class="stat-card">
        <h3>Solved Challenges</h3>
        <p class="stat-value">{{ stats.solvedChallenges }}</p>
      </div>
      <div class="stat-card">
        <h3>Total Points</h3>
        <p class="stat-value">{{ stats.totalPoints }}</p>
      </div>
      <div class="stat-card">
        <h3>Global Rank</h3>
        <p class="stat-value">#{{ stats.rank }}</p>
      </div>
      <div class="stat-card">
        <h3>Completion Rate</h3>
        <p class="stat-value">{{ stats.completionRate }}%</p>
      </div>
    </div>

    <div class="recent-activity">
      <h2>Recent Activity</h2>
      <div class="activity-list">
        <div v-for="activity in recentActivity" 
             :key="activity.id"
             class="activity-item">
          <div class="activity-icon" :class="activity.type"></div>
          <div class="activity-details">
            <h4>{{ activity.name }}</h4>
            <p class="activity-meta">
              <span class="points">+{{ activity.points }} points</span>
              <span class="date">{{ formatDate(activity.date) }}</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.profile {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  color: white;
}

.profile-header {
  margin-bottom: 3rem;
  text-align: center;

  h1 {
    color: #00ff88;
    margin-bottom: 0.5rem;
  }

  .email {
    opacity: 0.7;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  text-align: center;
  backdrop-filter: blur(10px);

  h3 {
    color: #00ff88;
    margin-bottom: 1rem;
    font-size: 1.1rem;
  }

  .stat-value {
    font-size: 2rem;
    font-weight: bold;
  }
}

.recent-activity {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 10px;
  backdrop-filter: blur(10px);

  h2 {
    color: #00ff88;
    margin-bottom: 1.5rem;
  }
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;

  .activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    &.challenge {
      background: rgba(0, 255, 136, 0.2);
    }

    &.competition {
      background: rgba(255, 215, 0, 0.2);
    }
  }

  .activity-details {
    flex: 1;

    h4 {
      margin-bottom: 0.5rem;
    }

    .activity-meta {
      display: flex;
      gap: 1rem;
      font-size: 0.9rem;
      opacity: 0.7;

      .points {
        color: #00ff88;
      }
    }
  }
}
</style>