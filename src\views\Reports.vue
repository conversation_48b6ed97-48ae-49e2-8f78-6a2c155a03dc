<script setup lang="ts">
import { ref, computed } from 'vue'

// 报告数据
const reportData = ref({
  overview: {
    totalSessions: 15,
    avgDuration: 285, // 秒
    attackSuccessRate: 68.5,
    defenseEfficiency: 72.3,
    lastUpdate: '2024-01-15 14:30:25'
  },
  skillAssessment: {
    penetrationTesting: 85,
    vulnerabilityAnalysis: 78,
    incidentResponse: 82,
    networkSecurity: 75,
    webSecurity: 88,
    systemHardening: 70
  },
  recentSessions: [
    {
      id: 1,
      date: '2024-01-15',
      scenario: 'Web应用渗透测试',
      duration: 320,
      attackSuccess: 75,
      defenseSuccess: 68,
      score: 82
    },
    {
      id: 2,
      date: '2024-01-14',
      scenario: '网络入侵检测',
      duration: 280,
      attackSuccess: 62,
      defenseSuccess: 85,
      score: 78
    },
    {
      id: 3,
      date: '2024-01-13',
      scenario: '云安全防护',
      duration: 310,
      attackSuccess: 70,
      defenseSuccess: 75,
      score: 85
    }
  ],
  vulnerabilityStats: [
    { type: 'SQL注入', discovered: 12, exploited: 8, blocked: 4 },
    { type: 'XSS攻击', discovered: 8, exploited: 5, blocked: 3 },
    { type: '文件上传', discovered: 6, exploited: 4, blocked: 2 },
    { type: '权限提升', discovered: 10, exploited: 6, blocked: 4 },
    { type: '横向移动', discovered: 5, exploited: 3, blocked: 2 }
  ]
})

// 选择的时间范围
const timeRange = ref('7days')
const timeRangeOptions = [
  { value: '7days', label: '最近7天' },
  { value: '30days', label: '最近30天' },
  { value: '90days', label: '最近90天' },
  { value: 'all', label: '全部时间' }
]

// 计算属性
const averageScore = computed(() => {
  const scores = reportData.value.recentSessions.map(s => s.score)
  return Math.round(scores.reduce((a, b) => a + b, 0) / scores.length)
})

const skillRadarData = computed(() => {
  const skills = reportData.value.skillAssessment
  return Object.entries(skills).map(([key, value]) => ({
    skill: key,
    score: value,
    label: getSkillLabel(key)
  }))
})

// 方法
const getSkillLabel = (skill: string) => {
  const labels = {
    penetrationTesting: '渗透测试',
    vulnerabilityAnalysis: '漏洞分析',
    incidentResponse: '应急响应',
    networkSecurity: '网络安全',
    webSecurity: 'Web安全',
    systemHardening: '系统加固'
  }
  return labels[skill as keyof typeof labels] || skill
}

const getScoreColor = (score: number) => {
  if (score >= 80) return '#00ff88'
  if (score >= 60) return '#ffa500'
  return '#ff4444'
}

const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}分${secs}秒`
}

const exportReport = () => {
  console.log('导出报告')
  alert('报告导出功能开发中...')
}

const generateDetailedReport = () => {
  console.log('生成详细报告')
  alert('正在生成详细报告...')
}
</script>

<template>
  <div class="reports">
    <div class="header">
      <h1>评估报告</h1>
      <div class="header-controls">
        <select v-model="timeRange" class="time-range-select">
          <option v-for="option in timeRangeOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </option>
        </select>
        <button @click="exportReport" class="export-btn">导出报告</button>
        <button @click="generateDetailedReport" class="generate-btn">生成详细报告</button>
      </div>
    </div>

    <div class="reports-grid">
      <!-- 概览统计 -->
      <div class="overview-panel">
        <h2>概览统计</h2>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">🎯</div>
            <div class="stat-content">
              <div class="stat-value">{{ reportData.overview.totalSessions }}</div>
              <div class="stat-label">推演次数</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">⏱️</div>
            <div class="stat-content">
              <div class="stat-value">{{ formatDuration(reportData.overview.avgDuration) }}</div>
              <div class="stat-label">平均时长</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">🔴</div>
            <div class="stat-content">
              <div class="stat-value">{{ reportData.overview.attackSuccessRate }}%</div>
              <div class="stat-label">攻击成功率</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">🛡️</div>
            <div class="stat-content">
              <div class="stat-value">{{ reportData.overview.defenseEfficiency }}%</div>
              <div class="stat-label">防御效率</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 技能雷达图 -->
      <div class="skill-radar">
        <h2>技能评估</h2>
        <div class="radar-container">
          <div class="radar-chart">
            <svg width="300" height="300" viewBox="0 0 300 300">
              <!-- 雷达图背景网格 -->
              <g class="radar-grid">
                <circle cx="150" cy="150" r="120" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
                <circle cx="150" cy="150" r="90" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
                <circle cx="150" cy="150" r="60" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
                <circle cx="150" cy="150" r="30" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
                
                <!-- 雷达图轴线 -->
                <line x1="150" y1="30" x2="150" y2="270" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
                <line x1="30" y1="150" x2="270" y2="150" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
                <line x1="253.9" y1="90" x2="46.1" y2="210" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
                <line x1="253.9" y1="210" x2="46.1" y2="90" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
              </g>
              
              <!-- 技能数据多边形 -->
              <polygon 
                :points="skillRadarData.map((skill, index) => {
                  const angle = (index * 60 - 90) * Math.PI / 180;
                  const radius = skill.score * 1.2;
                  const x = 150 + radius * Math.cos(angle);
                  const y = 150 + radius * Math.sin(angle);
                  return `${x},${y}`;
                }).join(' ')"
                fill="rgba(0,255,136,0.2)"
                stroke="#00ff88"
                stroke-width="2"
              />
              
              <!-- 技能点 -->
              <g v-for="(skill, index) in skillRadarData" :key="skill.skill">
                <circle 
                  :cx="150 + skill.score * 1.2 * Math.cos((index * 60 - 90) * Math.PI / 180)"
                  :cy="150 + skill.score * 1.2 * Math.sin((index * 60 - 90) * Math.PI / 180)"
                  r="4"
                  fill="#00ff88"
                />
              </g>
            </svg>
          </div>
          
          <div class="skill-legend">
            <div v-for="skill in skillRadarData" :key="skill.skill" class="skill-item">
              <span class="skill-name">{{ skill.label }}</span>
              <span class="skill-score" :style="{ color: getScoreColor(skill.score) }">{{ skill.score }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近推演记录 -->
      <div class="recent-sessions">
        <h2>最近推演记录</h2>
        <div class="sessions-list">
          <div v-for="session in reportData.recentSessions" :key="session.id" class="session-item">
            <div class="session-header">
              <span class="session-scenario">{{ session.scenario }}</span>
              <span class="session-date">{{ session.date }}</span>
            </div>
            <div class="session-metrics">
              <div class="metric">
                <span class="metric-label">时长:</span>
                <span class="metric-value">{{ formatDuration(session.duration) }}</span>
              </div>
              <div class="metric">
                <span class="metric-label">攻击成功:</span>
                <span class="metric-value" :style="{ color: getScoreColor(session.attackSuccess) }">
                  {{ session.attackSuccess }}%
                </span>
              </div>
              <div class="metric">
                <span class="metric-label">防御成功:</span>
                <span class="metric-value" :style="{ color: getScoreColor(session.defenseSuccess) }">
                  {{ session.defenseSuccess }}%
                </span>
              </div>
              <div class="metric">
                <span class="metric-label">综合评分:</span>
                <span class="metric-value" :style="{ color: getScoreColor(session.score) }">
                  {{ session.score }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 漏洞统计 -->
      <div class="vulnerability-stats">
        <h2>漏洞统计</h2>
        <div class="vuln-list">
          <div v-for="vuln in reportData.vulnerabilityStats" :key="vuln.type" class="vuln-item">
            <div class="vuln-type">{{ vuln.type }}</div>
            <div class="vuln-metrics">
              <div class="vuln-metric discovered">
                <span class="metric-label">发现:</span>
                <span class="metric-value">{{ vuln.discovered }}</span>
              </div>
              <div class="vuln-metric exploited">
                <span class="metric-label">利用:</span>
                <span class="metric-value">{{ vuln.exploited }}</span>
              </div>
              <div class="vuln-metric blocked">
                <span class="metric-label">阻断:</span>
                <span class="metric-value">{{ vuln.blocked }}</span>
              </div>
            </div>
            <div class="vuln-bar">
              <div class="bar-segment discovered" :style="{ width: (vuln.discovered / 15 * 100) + '%' }"></div>
              <div class="bar-segment exploited" :style="{ width: (vuln.exploited / 15 * 100) + '%' }"></div>
              <div class="bar-segment blocked" :style="{ width: (vuln.blocked / 15 * 100) + '%' }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.reports {
  padding: 2rem;
  max-width: 1600px;
  margin: 0 auto;
  color: white;
  padding-top: calc(70px + 2rem);
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  flex-wrap: wrap;
  gap: 1rem;

  h1 {
    color: #00ff88;
    font-size: 2.5rem;
    margin: 0;
  }

  .header-controls {
    display: flex;
    gap: 1rem;
    align-items: center;

    .time-range-select {
      padding: 0.5rem 1rem;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      color: white;
      cursor: pointer;

      &:focus {
        outline: none;
        border-color: #00ff88;
      }

      option {
        background: #1a1a1a;
      }
    }

    .export-btn, .generate-btn {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 8px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .export-btn {
      background: transparent;
      color: #00ff88;
      border: 2px solid #00ff88;

      &:hover {
        background: #00ff88;
        color: black;
      }
    }

    .generate-btn {
      background: linear-gradient(45deg, #00ff88, #00a3ff);
      color: white;

      &:hover {
        transform: translateY(-2px);
      }
    }
  }
}

.reports-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 2rem;
}

.overview-panel, .skill-radar, .recent-sessions, .vulnerability-stats {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 2rem;
  backdrop-filter: blur(10px);

  h2 {
    color: #00ff88;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
  }
}

.overview-panel {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .stat-card {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;

    .stat-icon {
      font-size: 2rem;
    }

    .stat-content {
      .stat-value {
        color: white;
        font-size: 1.5rem;
        font-weight: bold;
        line-height: 1;
      }

      .stat-label {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
      }
    }
  }
}

.skill-radar {
  .radar-container {
    display: flex;
    gap: 2rem;
    align-items: center;
  }

  .radar-chart {
    flex-shrink: 0;
  }

  .skill-legend {
    flex: 1;

    .skill-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.8rem;
      padding: 0.5rem;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 5px;

      .skill-name {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
      }

      .skill-score {
        font-weight: bold;
        font-size: 0.9rem;
      }
    }
  }
}

.recent-sessions {
  .sessions-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .session-item {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 1rem;

    .session-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.8rem;

      .session-scenario {
        color: white;
        font-weight: 500;
      }

      .session-date {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.9rem;
      }
    }

    .session-metrics {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0.5rem;

      .metric {
        display: flex;
        justify-content: space-between;
        font-size: 0.9rem;

        .metric-label {
          color: rgba(255, 255, 255, 0.7);
        }

        .metric-value {
          font-weight: 500;
        }
      }
    }
  }
}

.vulnerability-stats {
  grid-column: 1 / -1;

  .vuln-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .vuln-item {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 1rem;

    .vuln-type {
      color: white;
      font-weight: 500;
      margin-bottom: 0.8rem;
    }

    .vuln-metrics {
      display: flex;
      gap: 2rem;
      margin-bottom: 0.8rem;

      .vuln-metric {
        display: flex;
        gap: 0.5rem;
        font-size: 0.9rem;

        .metric-label {
          color: rgba(255, 255, 255, 0.7);
        }

        .metric-value {
          font-weight: 500;
        }

        &.discovered .metric-value {
          color: #00a3ff;
        }

        &.exploited .metric-value {
          color: #ff4444;
        }

        &.blocked .metric-value {
          color: #00ff88;
        }
      }
    }

    .vuln-bar {
      height: 6px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
      overflow: hidden;
      position: relative;

      .bar-segment {
        height: 100%;
        position: absolute;
        top: 0;

        &.discovered {
          background: #00a3ff;
          left: 0;
        }

        &.exploited {
          background: #ff4444;
          left: 33.33%;
        }

        &.blocked {
          background: #00ff88;
          left: 66.66%;
        }
      }
    }
  }
}

/* 桌面端适配 */
@media (min-width: 768px) {
  .reports {
    padding: 3rem;
    padding-top: calc(80px + 3rem);
  }

  .header h1 {
    font-size: 3rem;
  }
}

@media (max-width: 1200px) {
  .reports-grid {
    grid-template-columns: 1fr;
  }

  .skill-radar .radar-container {
    flex-direction: column;
    text-align: center;
  }

  .overview-panel .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
