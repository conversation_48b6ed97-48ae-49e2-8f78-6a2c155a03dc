<script setup lang="ts">
import { ref, computed } from 'vue'

// 场景配置数据
const scenarioName = ref('')
const selectedTemplate = ref('')
const networkTopology = ref('simple')
const businessType = ref('web_app')
const vulnerabilityLevel = ref('medium')
const customConfig = ref('')

// 预设模板
const templates = [
  {
    id: 'web_pentest',
    name: 'Web应用渗透测试',
    description: '包含常见Web漏洞的企业应用场景',
    icon: '🌐'
  },
  {
    id: 'network_intrusion',
    name: '网络入侵检测',
    description: '模拟APT攻击的企业网络环境',
    icon: '🔒'
  },
  {
    id: 'cloud_security',
    name: '云安全防护',
    description: '云原生应用的安全防护场景',
    icon: '☁️'
  },
  {
    id: 'iot_security',
    name: 'IoT设备安全',
    description: '物联网设备的安全测试环境',
    icon: '📱'
  }
]

// 网络拓扑选项
const topologyOptions = [
  { value: 'simple', label: '简单网络', description: '单一子网，适合基础测试' },
  { value: 'enterprise', label: '企业网络', description: '多层网络架构，包含DMZ' },
  { value: 'cloud', label: '云环境', description: '混合云架构，容器化部署' },
  { value: 'custom', label: '自定义', description: '完全自定义网络拓扑' }
]

// 业务类型选项
const businessOptions = [
  { value: 'web_app', label: 'Web应用', icon: '🌐' },
  { value: 'api_service', label: 'API服务', icon: '🔌' },
  { value: 'database', label: '数据库系统', icon: '🗄️' },
  { value: 'file_server', label: '文件服务器', icon: '📁' },
  { value: 'email_system', label: '邮件系统', icon: '📧' },
  { value: 'erp_system', label: 'ERP系统', icon: '💼' }
]

// 漏洞等级选项
const vulnerabilityOptions = [
  { value: 'low', label: '低危', color: '#00ff88' },
  { value: 'medium', label: '中危', color: '#ffa500' },
  { value: 'high', label: '高危', color: '#ff4444' },
  { value: 'critical', label: '严重', color: '#8b0000' }
]

// 表单验证
const isFormValid = computed(() => {
  return scenarioName.value.trim() && selectedTemplate.value
})

// 生成场景
const generateScenario = () => {
  if (!isFormValid.value) return
  
  const config = {
    name: scenarioName.value,
    template: selectedTemplate.value,
    topology: networkTopology.value,
    business: businessType.value,
    vulnerability: vulnerabilityLevel.value,
    custom: customConfig.value
  }
  
  console.log('生成场景配置:', config)
  // 这里会调用后端API生成场景
  alert('场景生成中，请稍候...')
}

// 保存配置
const saveConfig = () => {
  console.log('保存配置')
  alert('配置已保存')
}

// 加载预设
const loadPreset = (templateId: string) => {
  selectedTemplate.value = templateId
  const template = templates.find(t => t.id === templateId)
  if (template) {
    scenarioName.value = template.name + '_' + Date.now()
  }
}
</script>

<template>
  <div class="scenario-config">
    <div class="header">
      <h1>场景配置</h1>
      <p class="subtitle">配置AI攻防推演场景，自定义网络拓扑和业务环境</p>
    </div>

    <div class="config-container">
      <!-- 左侧配置面板 -->
      <div class="config-panel">
        <div class="section">
          <h2>基础配置</h2>
          
          <div class="form-group">
            <label>场景名称</label>
            <input 
              v-model="scenarioName" 
              type="text" 
              placeholder="请输入场景名称"
              class="form-input"
            >
          </div>

          <div class="form-group">
            <label>预设模板</label>
            <div class="template-grid">
              <div 
                v-for="template in templates" 
                :key="template.id"
                :class="['template-card', { active: selectedTemplate === template.id }]"
                @click="loadPreset(template.id)"
              >
                <div class="template-icon">{{ template.icon }}</div>
                <h3>{{ template.name }}</h3>
                <p>{{ template.description }}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="section">
          <h2>网络拓扑</h2>
          <div class="topology-options">
            <div 
              v-for="option in topologyOptions" 
              :key="option.value"
              :class="['topology-option', { active: networkTopology === option.value }]"
              @click="networkTopology = option.value"
            >
              <h4>{{ option.label }}</h4>
              <p>{{ option.description }}</p>
            </div>
          </div>
        </div>

        <div class="section">
          <h2>业务环境</h2>
          <div class="business-grid">
            <div 
              v-for="business in businessOptions" 
              :key="business.value"
              :class="['business-card', { active: businessType === business.value }]"
              @click="businessType = business.value"
            >
              <div class="business-icon">{{ business.icon }}</div>
              <span>{{ business.label }}</span>
            </div>
          </div>
        </div>

        <div class="section">
          <h2>漏洞等级</h2>
          <div class="vulnerability-options">
            <div 
              v-for="vuln in vulnerabilityOptions" 
              :key="vuln.value"
              :class="['vuln-option', { active: vulnerabilityLevel === vuln.value }]"
              :style="{ borderColor: vulnerabilityLevel === vuln.value ? vuln.color : 'transparent' }"
              @click="vulnerabilityLevel = vuln.value"
            >
              <div class="vuln-indicator" :style="{ backgroundColor: vuln.color }"></div>
              <span>{{ vuln.label }}</span>
            </div>
          </div>
        </div>

        <div class="section">
          <h2>自定义配置</h2>
          <textarea 
            v-model="customConfig"
            placeholder="输入自定义配置（JSON格式）"
            class="custom-config"
            rows="6"
          ></textarea>
        </div>

        <div class="actions">
          <button @click="saveConfig" class="btn secondary">保存配置</button>
          <button 
            @click="generateScenario" 
            :disabled="!isFormValid"
            class="btn primary"
          >
            生成场景
          </button>
        </div>
      </div>

      <!-- 右侧预览面板 -->
      <div class="preview-panel">
        <h2>场景预览</h2>
        <div class="topology-preview">
          <div class="preview-placeholder">
            <div class="network-diagram">
              <div class="node attacker">🔴 攻击者</div>
              <div class="node target">🎯 目标系统</div>
              <div class="node defender">🛡️ 防御系统</div>
              <div class="connection"></div>
            </div>
            <p>网络拓扑图将在这里显示</p>
          </div>
        </div>

        <div class="config-summary">
          <h3>配置摘要</h3>
          <div class="summary-item">
            <span class="label">场景名称:</span>
            <span class="value">{{ scenarioName || '未设置' }}</span>
          </div>
          <div class="summary-item">
            <span class="label">网络拓扑:</span>
            <span class="value">{{ topologyOptions.find(t => t.value === networkTopology)?.label }}</span>
          </div>
          <div class="summary-item">
            <span class="label">业务类型:</span>
            <span class="value">{{ businessOptions.find(b => b.value === businessType)?.label }}</span>
          </div>
          <div class="summary-item">
            <span class="label">漏洞等级:</span>
            <span class="value">{{ vulnerabilityOptions.find(v => v.value === vulnerabilityLevel)?.label }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.scenario-config {
  padding: 2rem;
  max-width: 1600px;
  margin: 0 auto;
  color: white;
  padding-top: calc(70px + 2rem);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 3rem;

  h1 {
    color: #00ff88;
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
  }
}

.config-container {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 3rem;
}

.config-panel {
  .section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    backdrop-filter: blur(10px);

    h2 {
      color: #00ff88;
      margin-bottom: 1.5rem;
      font-size: 1.3rem;
    }
  }

  .form-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #00ff88;
      font-weight: 500;
    }

    .form-input {
      width: 100%;
      padding: 0.8rem;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      color: white;
      font-size: 1rem;

      &:focus {
        outline: none;
        border-color: #00ff88;
        box-shadow: 0 0 0 2px rgba(0, 255, 136, 0.2);
      }
    }
  }

  .template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .template-card {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(0, 255, 136, 0.5);
      transform: translateY(-2px);
    }

    &.active {
      border-color: #00ff88;
      background: rgba(0, 255, 136, 0.1);
    }

    .template-icon {
      font-size: 2rem;
      margin-bottom: 0.5rem;
    }

    h3 {
      color: white;
      margin-bottom: 0.5rem;
      font-size: 1rem;
    }

    p {
      color: rgba(255, 255, 255, 0.7);
      font-size: 0.85rem;
      line-height: 1.4;
    }
  }
}

.topology-options {
  display: grid;
  gap: 1rem;
}

.topology-option {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(0, 255, 136, 0.5);
  }

  &.active {
    border-color: #00ff88;
    background: rgba(0, 255, 136, 0.1);
  }

  h4 {
    color: white;
    margin-bottom: 0.3rem;
  }

  p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
  }
}

.business-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.business-card {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(0, 255, 136, 0.5);
  }

  &.active {
    border-color: #00ff88;
    background: rgba(0, 255, 136, 0.1);
  }

  .business-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  span {
    color: white;
    font-size: 0.9rem;
  }
}

.vulnerability-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
}

.vuln-option {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;

  &:hover {
    transform: translateY(-2px);
  }

  .vuln-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }

  span {
    color: white;
    font-size: 0.9rem;
  }
}

.custom-config {
  width: 100%;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-family: 'Courier New', monospace;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: #00ff88;
    box-shadow: 0 0 0 2px rgba(0, 255, 136, 0.2);
  }
}

.actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;

  .btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;

    &.primary {
      background: linear-gradient(45deg, #00ff88, #00a3ff);
      color: white;

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    &.secondary {
      background: transparent;
      color: #00ff88;
      border: 2px solid #00ff88;

      &:hover {
        background: #00ff88;
        color: black;
      }
    }
  }
}

.preview-panel {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  height: fit-content;
  position: sticky;
  top: 100px;

  h2 {
    color: #00ff88;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
  }

  .topology-preview {
    margin-bottom: 2rem;

    .preview-placeholder {
      background: rgba(0, 0, 0, 0.3);
      border: 2px dashed rgba(255, 255, 255, 0.3);
      border-radius: 10px;
      padding: 2rem;
      text-align: center;
      min-height: 200px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .network-diagram {
        display: flex;
        justify-content: space-around;
        align-items: center;
        width: 100%;
        margin-bottom: 1rem;
        position: relative;

        .node {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          padding: 1rem;
          font-size: 0.8rem;
          text-align: center;
          min-width: 60px;
          min-height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .connection {
          position: absolute;
          top: 50%;
          left: 20%;
          right: 20%;
          height: 2px;
          background: linear-gradient(90deg, #00ff88, #00a3ff);
          transform: translateY(-50%);
        }
      }

      p {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.9rem;
      }
    }
  }

  .config-summary {
    h3 {
      color: #00ff88;
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.8rem;
      padding: 0.5rem 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      .label {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
      }

      .value {
        color: white;
        font-weight: 500;
        font-size: 0.9rem;
      }
    }
  }
}

/* 桌面端适配 */
@media (min-width: 768px) {
  .scenario-config {
    padding: 3rem;
    padding-top: calc(80px + 3rem);
  }

  .header h1 {
    font-size: 3rem;
  }
}

@media (max-width: 1200px) {
  .config-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .preview-panel {
    position: static;
  }
}
</style>
