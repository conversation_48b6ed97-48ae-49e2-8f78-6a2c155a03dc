<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 推演状态
const isRunning = ref(false)
const currentTime = ref(0)
const totalTime = ref(300) // 5分钟
const currentPhase = ref('准备阶段')

// 攻防数据
const attackEvents = ref([
  { id: 1, time: '10:23:45', type: 'scan', source: '*************', target: '********', status: 'success', description: '端口扫描发现开放服务' },
  { id: 2, time: '10:24:12', type: 'exploit', source: '*************', target: '********', status: 'blocked', description: 'SQL注入攻击被WAF拦截' },
  { id: 3, time: '10:24:38', type: 'lateral', source: '********', target: '********', status: 'success', description: '横向移动获取内网权限' }
])

const defenseEvents = ref([
  { id: 1, time: '10:23:50', type: 'detect', action: 'alert', target: '*************', description: '检测到异常扫描行为' },
  { id: 2, time: '10:24:15', type: 'block', action: 'firewall', target: '*************', description: '防火墙阻断恶意请求' },
  { id: 3, time: '10:24:45', type: 'isolate', action: 'quarantine', target: '********', description: '隔离受感染主机' }
])

// 网络节点状态
const networkNodes = ref([
  { id: 'web-server', name: 'Web服务器', ip: '********', status: 'compromised', x: 200, y: 150 },
  { id: 'db-server', name: '数据库服务器', ip: '********', status: 'normal', x: 400, y: 150 },
  { id: 'firewall', name: '防火墙', ip: '********', status: 'active', x: 100, y: 100 },
  { id: 'attacker', name: '攻击者', ip: '*************', status: 'attacking', x: 50, y: 200 }
])

// 实时指标
const metrics = ref({
  attackSuccess: 65,
  defenseEfficiency: 78,
  systemIntegrity: 82,
  networkTraffic: 1250
})

// 时间轴控制
let timeInterval: number | null = null

// 开始/暂停推演
const toggleSimulation = () => {
  isRunning.value = !isRunning.value
  
  if (isRunning.value) {
    startSimulation()
  } else {
    pauseSimulation()
  }
}

// 开始推演
const startSimulation = () => {
  timeInterval = setInterval(() => {
    currentTime.value += 1
    
    // 更新阶段
    if (currentTime.value < 60) {
      currentPhase.value = '侦察阶段'
    } else if (currentTime.value < 180) {
      currentPhase.value = '攻击阶段'
    } else if (currentTime.value < 240) {
      currentPhase.value = '横向移动'
    } else {
      currentPhase.value = '数据窃取'
    }
    
    // 模拟随机事件
    if (Math.random() < 0.1) {
      addRandomEvent()
    }
    
    // 更新指标
    updateMetrics()
    
    // 检查是否结束
    if (currentTime.value >= totalTime.value) {
      stopSimulation()
    }
  }, 1000)
}

// 暂停推演
const pauseSimulation = () => {
  if (timeInterval) {
    clearInterval(timeInterval)
    timeInterval = null
  }
}

// 停止推演
const stopSimulation = () => {
  isRunning.value = false
  pauseSimulation()
  currentPhase.value = '推演结束'
}

// 重置推演
const resetSimulation = () => {
  stopSimulation()
  currentTime.value = 0
  currentPhase.value = '准备阶段'
  // 重置所有数据
  attackEvents.value = []
  defenseEvents.value = []
  metrics.value = {
    attackSuccess: 0,
    defenseEfficiency: 100,
    systemIntegrity: 100,
    networkTraffic: 0
  }
}

// 添加随机事件
const addRandomEvent = () => {
  const eventTypes = ['scan', 'exploit', 'lateral', 'exfiltrate']
  const randomType = eventTypes[Math.floor(Math.random() * eventTypes.length)]
  
  const newEvent = {
    id: Date.now(),
    time: formatTime(currentTime.value),
    type: randomType,
    source: '*************',
    target: '10.0.0.' + (Math.floor(Math.random() * 10) + 1),
    status: Math.random() > 0.3 ? 'success' : 'blocked',
    description: `${randomType} 操作 - ${Math.random() > 0.5 ? '成功' : '失败'}`
  }
  
  attackEvents.value.unshift(newEvent)
  
  // 限制事件数量
  if (attackEvents.value.length > 10) {
    attackEvents.value.pop()
  }
}

// 更新指标
const updateMetrics = () => {
  metrics.value.attackSuccess = Math.min(100, metrics.value.attackSuccess + Math.random() * 2)
  metrics.value.defenseEfficiency = Math.max(0, metrics.value.defenseEfficiency - Math.random() * 1.5)
  metrics.value.systemIntegrity = Math.max(0, metrics.value.systemIntegrity - Math.random() * 1)
  metrics.value.networkTraffic = Math.floor(Math.random() * 2000) + 500
}

// 格式化时间
const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 获取节点状态颜色
const getNodeColor = (status: string) => {
  const colors = {
    normal: '#00ff88',
    compromised: '#ff4444',
    active: '#00a3ff',
    attacking: '#ff6600'
  }
  return colors[status as keyof typeof colors] || '#ffffff'
}

// 获取事件类型图标
const getEventIcon = (type: string) => {
  const icons = {
    scan: '🔍',
    exploit: '💥',
    lateral: '➡️',
    exfiltrate: '📤',
    detect: '👁️',
    block: '🛡️',
    isolate: '🔒'
  }
  return icons[type as keyof typeof icons] || '❓'
}

onMounted(() => {
  // 初始化一些示例数据
  console.log('实时推演页面已加载')
})

onUnmounted(() => {
  pauseSimulation()
})
</script>

<template>
  <div class="simulation">
    <div class="header">
      <h1>实时攻防推演</h1>
      <div class="simulation-controls">
        <button @click="toggleSimulation" :class="['control-btn', isRunning ? 'pause' : 'play']">
          {{ isRunning ? '⏸️ 暂停' : '▶️ 开始' }}
        </button>
        <button @click="resetSimulation" class="control-btn reset">🔄 重置</button>
        <div class="time-display">
          <span class="current-time">{{ formatTime(currentTime) }}</span>
          <span class="separator">/</span>
          <span class="total-time">{{ formatTime(totalTime) }}</span>
        </div>
        <div class="phase-indicator">
          <span class="phase-label">当前阶段:</span>
          <span class="phase-name">{{ currentPhase }}</span>
        </div>
      </div>
    </div>

    <div class="simulation-content">
      <!-- 网络拓扑可视化 -->
      <div class="network-visualization">
        <h2>网络拓扑</h2>
        <div class="network-canvas">
          <svg width="100%" height="300" viewBox="0 0 500 300">
            <!-- 连接线 -->
            <line x1="50" y1="200" x2="100" y2="100" stroke="#00ff88" stroke-width="2" opacity="0.6"/>
            <line x1="100" y1="100" x2="200" y2="150" stroke="#00ff88" stroke-width="2" opacity="0.6"/>
            <line x1="200" y1="150" x2="400" y2="150" stroke="#ff4444" stroke-width="3" opacity="0.8"/>
            
            <!-- 网络节点 -->
            <g v-for="node in networkNodes" :key="node.id">
              <circle 
                :cx="node.x" 
                :cy="node.y" 
                r="20" 
                :fill="getNodeColor(node.status)"
                :stroke="getNodeColor(node.status)"
                stroke-width="2"
                opacity="0.8"
              />
              <text 
                :x="node.x" 
                :y="node.y - 30" 
                text-anchor="middle" 
                fill="white" 
                font-size="12"
              >
                {{ node.name }}
              </text>
              <text 
                :x="node.x" 
                :y="node.y + 40" 
                text-anchor="middle" 
                fill="rgba(255,255,255,0.7)" 
                font-size="10"
              >
                {{ node.ip }}
              </text>
            </g>
            
            <!-- 攻击路径动画 -->
            <circle v-if="isRunning" r="3" fill="#ff6600" opacity="0.8">
              <animateMotion dur="3s" repeatCount="indefinite">
                <path d="M50,200 L100,100 L200,150 L400,150"/>
              </animateMotion>
            </circle>
          </svg>
        </div>
      </div>

      <!-- 实时指标 -->
      <div class="metrics-panel">
        <h2>实时指标</h2>
        <div class="metrics-grid">
          <div class="metric-card">
            <div class="metric-label">攻击成功率</div>
            <div class="metric-value">{{ metrics.attackSuccess.toFixed(1) }}%</div>
            <div class="metric-bar">
              <div class="metric-fill" :style="{ width: metrics.attackSuccess + '%', backgroundColor: '#ff4444' }"></div>
            </div>
          </div>
          
          <div class="metric-card">
            <div class="metric-label">防御效率</div>
            <div class="metric-value">{{ metrics.defenseEfficiency.toFixed(1) }}%</div>
            <div class="metric-bar">
              <div class="metric-fill" :style="{ width: metrics.defenseEfficiency + '%', backgroundColor: '#00ff88' }"></div>
            </div>
          </div>
          
          <div class="metric-card">
            <div class="metric-label">系统完整性</div>
            <div class="metric-value">{{ metrics.systemIntegrity.toFixed(1) }}%</div>
            <div class="metric-bar">
              <div class="metric-fill" :style="{ width: metrics.systemIntegrity + '%', backgroundColor: '#00a3ff' }"></div>
            </div>
          </div>
          
          <div class="metric-card">
            <div class="metric-label">网络流量</div>
            <div class="metric-value">{{ metrics.networkTraffic }} KB/s</div>
            <div class="metric-bar">
              <div class="metric-fill" :style="{ width: Math.min(100, metrics.networkTraffic / 20) + '%', backgroundColor: '#ffa500' }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 事件日志 -->
      <div class="events-panel">
        <div class="events-section">
          <h3>🔴 攻击事件</h3>
          <div class="event-list">
            <div v-for="event in attackEvents" :key="event.id" class="event-item attack">
              <div class="event-icon">{{ getEventIcon(event.type) }}</div>
              <div class="event-details">
                <div class="event-time">{{ event.time }}</div>
                <div class="event-description">{{ event.description }}</div>
                <div class="event-meta">
                  <span>{{ event.source }} → {{ event.target }}</span>
                  <span :class="['event-status', event.status]">{{ event.status === 'success' ? '成功' : '被阻断' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="events-section">
          <h3>🛡️ 防御事件</h3>
          <div class="event-list">
            <div v-for="event in defenseEvents" :key="event.id" class="event-item defense">
              <div class="event-icon">{{ getEventIcon(event.type) }}</div>
              <div class="event-details">
                <div class="event-time">{{ event.time }}</div>
                <div class="event-description">{{ event.description }}</div>
                <div class="event-meta">
                  <span>目标: {{ event.target }}</span>
                  <span class="event-action">{{ event.action }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.simulation {
  padding: 2rem;
  max-width: 1600px;
  margin: 0 auto;
  color: white;
  padding-top: calc(70px + 2rem);
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;

  h1 {
    color: #00ff88;
    font-size: 2.5rem;
    margin: 0;
  }

  .simulation-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;

    .control-btn {
      padding: 0.8rem 1.5rem;
      border: none;
      border-radius: 8px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      &.play {
        background: linear-gradient(45deg, #00ff88, #00a3ff);
        color: white;
      }

      &.pause {
        background: #ffa500;
        color: white;
      }

      &.reset {
        background: transparent;
        color: #ff4444;
        border: 2px solid #ff4444;
      }

      &:hover {
        transform: translateY(-2px);
      }
    }

    .time-display {
      background: rgba(255, 255, 255, 0.1);
      padding: 0.5rem 1rem;
      border-radius: 8px;
      font-family: 'Courier New', monospace;
      font-size: 1.2rem;

      .current-time {
        color: #00ff88;
        font-weight: bold;
      }

      .separator {
        color: rgba(255, 255, 255, 0.5);
        margin: 0 0.5rem;
      }

      .total-time {
        color: rgba(255, 255, 255, 0.7);
      }
    }

    .phase-indicator {
      background: rgba(0, 255, 136, 0.1);
      border: 1px solid #00ff88;
      padding: 0.5rem 1rem;
      border-radius: 8px;

      .phase-label {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
      }

      .phase-name {
        color: #00ff88;
        font-weight: bold;
        margin-left: 0.5rem;
      }
    }
  }
}

.simulation-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  gap: 2rem;
}

.network-visualization {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 2rem;
  backdrop-filter: blur(10px);

  h2 {
    color: #00ff88;
    margin-bottom: 1rem;
    font-size: 1.3rem;
  }

  .network-canvas {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid rgba(0, 255, 136, 0.2);
  }
}

.metrics-panel {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 2rem;
  backdrop-filter: blur(10px);

  h2 {
    color: #00ff88;
    margin-bottom: 1rem;
    font-size: 1.3rem;
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .metric-card {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);

    .metric-label {
      color: rgba(255, 255, 255, 0.7);
      font-size: 0.9rem;
      margin-bottom: 0.5rem;
    }

    .metric-value {
      color: white;
      font-size: 1.5rem;
      font-weight: bold;
      margin-bottom: 0.5rem;
    }

    .metric-bar {
      width: 100%;
      height: 6px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
      overflow: hidden;

      .metric-fill {
        height: 100%;
        border-radius: 3px;
        transition: width 0.5s ease;
      }
    }
  }
}

.events-panel {
  grid-column: 1 / -1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;

  .events-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);

    h3 {
      color: #00ff88;
      margin-bottom: 1rem;
      font-size: 1.2rem;
    }

    .event-list {
      max-height: 300px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 0.8rem;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #00ff88;
        border-radius: 3px;
      }
    }

    .event-item {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      padding: 1rem;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 8px;
      border-left: 3px solid;

      &.attack {
        border-left-color: #ff4444;
      }

      &.defense {
        border-left-color: #00ff88;
      }

      .event-icon {
        font-size: 1.2rem;
        flex-shrink: 0;
      }

      .event-details {
        flex: 1;

        .event-time {
          color: #00ff88;
          font-size: 0.8rem;
          font-family: 'Courier New', monospace;
          margin-bottom: 0.3rem;
        }

        .event-description {
          color: white;
          font-size: 0.9rem;
          margin-bottom: 0.5rem;
        }

        .event-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 0.8rem;
          color: rgba(255, 255, 255, 0.6);

          .event-status {
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.7rem;

            &.success {
              background: rgba(255, 68, 68, 0.2);
              color: #ff4444;
            }

            &.blocked {
              background: rgba(0, 255, 136, 0.2);
              color: #00ff88;
            }
          }

          .event-action {
            background: rgba(0, 163, 255, 0.2);
            color: #00a3ff;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.7rem;
          }
        }
      }
    }
  }
}

/* 桌面端适配 */
@media (min-width: 768px) {
  .simulation {
    padding: 3rem;
    padding-top: calc(80px + 3rem);
  }

  .header h1 {
    font-size: 3rem;
  }
}

@media (max-width: 1200px) {
  .simulation-content {
    grid-template-columns: 1fr;
  }

  .events-panel {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: stretch;

    .simulation-controls {
      justify-content: center;
    }
  }

  .metrics-panel .metrics-grid {
    grid-template-columns: 1fr;
  }
}
</style>
