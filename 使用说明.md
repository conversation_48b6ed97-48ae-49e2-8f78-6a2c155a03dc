# AI 攻防推演平台 - 使用说明

## 🎯 平台概述

这是一个基于AI技术的动态攻防推演平台，将传统的CTF（Capture The Flag）竞赛模式升级为智能化的攻防对抗演练系统。平台通过AI Agent模拟真实的攻击和防御行为，为用户提供沉浸式的网络安全学习和评估体验。

## 🚀 快速开始

### 1. 访问平台
- 打开浏览器访问平台首页
- 首页展示平台统计数据、实时活动和热门场景
- 可以看到当前活跃用户数、完成会话数等实时信息

### 2. 用户注册/登录
- 点击右上角"立即注册"或"登录"按钮
- 填写邮箱和密码完成注册
- 登录后可以访问所有功能模块

### 3. 浏览功能模块
平台包含以下主要功能：
- **🏠 首页** - 平台概览和实时数据
- **🎯 场景配置** - 创建和配置推演场景
- **⚡ 实时推演** - 观看AI攻防对抗过程
- **🤖 AI控制台** - 调整AI参数和策略
- **📊 评估报告** - 查看技能评估和分析报告
- **👤 个人中心** - 查看个人统计和历史记录

## 📋 功能详解

### 🎯 场景配置
**用途**: 创建自定义的攻防推演场景

**操作步骤**:
1. 进入"场景配置"页面
2. 选择预设模板（Web安全、网络安全、云安全、IoT安全）
3. 配置场景参数：
   - **场景名称**: 为场景起一个描述性名称
   - **网络拓扑**: 选择网络结构类型
   - **业务环境**: 选择模拟的业务类型
   - **漏洞等级**: 设置漏洞的严重程度
4. 点击"生成场景"创建新场景
5. 可以在网络拓扑编辑器中调整节点位置

**预设模板说明**:
- **Web应用渗透测试**: 模拟Web应用安全测试场景
- **网络入侵检测**: 模拟企业网络入侵检测场景
- **云安全防护**: 模拟云环境安全防护场景
- **IoT设备安全**: 模拟物联网设备安全测试场景

### ⚡ 实时推演
**用途**: 观看AI攻防对抗的实时过程

**界面说明**:
- **控制面板**: 开始/暂停/重置推演，显示时间和阶段
- **网络拓扑**: 可视化显示网络节点和攻击路径
- **实时指标**: 显示攻击成功率、防御效率等关键指标
- **事件日志**: 实时显示攻击和防御事件

**操作方法**:
1. 点击"▶️ 开始"按钮启动推演
2. 观察网络拓扑中的攻击路径动画
3. 监控实时指标的变化
4. 查看攻击和防御事件日志
5. 可以随时暂停或重置推演

**推演阶段**:
- **侦察阶段** (0-1分钟): AI进行环境扫描和信息收集
- **攻击阶段** (1-3分钟): AI执行漏洞利用和权限提升
- **横向移动** (3-4分钟): AI在网络中进行横向移动
- **数据窃取** (4-5分钟): AI执行数据窃取和持久化

### 🤖 AI控制台
**用途**: 调整AI Agent的行为参数和策略

**攻击者AI配置**:
- **攻击性** (0-100%): 控制攻击的激进程度
- **智能度** (0-100%): 控制AI的决策智能水平
- **隐蔽性** (0-100%): 控制攻击的隐蔽程度
- **持久性** (0-100%): 控制持久化攻击的能力
- **攻击策略**: 选择激进型、隐蔽型、自适应或持久型

**防御者AI配置**:
- **警觉性** (0-100%): 控制威胁检测的敏感度
- **响应速度** (0-100%): 控制防御响应的速度
- **覆盖范围** (0-100%): 控制防御监控的覆盖面
- **适应性** (0-100%): 控制防御策略的适应能力
- **防御策略**: 选择被动防御、主动防御、自适应或零信任

**决策日志**:
- 实时显示AI的决策过程和推理逻辑
- 包含决策内容、推理原因、置信度和结果
- 可以导出日志进行分析

### 📊 评估报告
**用途**: 查看技能评估和性能分析报告

**报告内容**:
- **概览统计**: 推演次数、平均时长、成功率等
- **技能雷达图**: 多维度技能评估可视化
- **最近推演记录**: 历史推演会话的详细信息
- **漏洞统计**: 发现、利用、阻断的漏洞统计

**技能评估维度**:
- 渗透测试、漏洞分析、应急响应
- 网络安全、Web安全、系统加固

**操作功能**:
- 选择时间范围查看历史数据
- 导出报告为PDF或Excel格式
- 生成详细的评估报告

### 👤 个人中心
**用途**: 查看个人统计信息和历史记录

**统计信息**:
- 完成推演次数和总推演次数
- 平均评分和推演总时长
- 全球排名和技能等级

**技能评估**:
- 各项技能的当前水平和进度
- 技能提升建议和学习路径

**最近活动**:
- 最近完成的推演会话
- 生成的评估报告
- 获得的成就和奖励

## 🎮 使用技巧

### 新手建议
1. **从简单场景开始**: 选择"Web应用渗透测试"模板入门
2. **观察AI行为**: 仔细观察AI的决策过程和攻击路径
3. **调整参数实验**: 尝试不同的AI参数组合，观察效果差异
4. **查看决策日志**: 学习AI的推理逻辑和决策过程
5. **定期查看报告**: 通过评估报告了解自己的技能发展

### 进阶技巧
1. **自定义场景**: 根据实际需求配置复杂的网络环境
2. **策略对比**: 尝试不同的AI策略组合，找到最优配置
3. **数据分析**: 导出决策日志和评估报告进行深度分析
4. **持续学习**: 根据评估报告的建议进行针对性学习

## 🔧 技术特性

### AI技术
- **智能决策**: AI基于当前环境和历史经验做出决策
- **自适应学习**: AI能够根据对手行为调整策略
- **实时推理**: 提供AI决策的详细推理过程

### 可视化
- **网络拓扑**: 直观显示网络结构和攻击路径
- **实时动画**: 攻击过程的动态可视化
- **数据图表**: 多维度的数据可视化展示

### 评估系统
- **多维评估**: 从多个角度评估用户技能水平
- **智能分析**: 基于AI的智能分析和建议
- **趋势追踪**: 长期技能发展趋势分析

## 🎯 学习目标

通过使用本平台，用户可以：

1. **理解攻防原理**: 深入了解网络攻击和防御的基本原理
2. **掌握安全技能**: 提升渗透测试、漏洞分析等安全技能
3. **培养安全思维**: 建立系统性的网络安全思维模式
4. **实战经验积累**: 通过模拟实战积累宝贵经验
5. **持续技能提升**: 通过量化评估持续改进技能水平

## 🆘 常见问题

**Q: 推演无法启动怎么办？**
A: 请确保已经配置了场景，然后刷新页面重试。

**Q: AI决策看起来不合理？**
A: AI的决策基于当前配置参数，可以尝试调整参数或重置AI状态。

**Q: 如何提高技能评分？**
A: 多参与不同类型的推演，仔细观察AI的决策过程，并根据评估报告的建议进行学习。

**Q: 数据会保存吗？**
A: 推演数据和评估报告会保存在本地，建议定期导出备份。

**Q: 支持团队协作吗？**
A: 当前版本主要支持个人学习，团队功能在后续版本中会加入。

## 📞 技术支持

如果在使用过程中遇到问题，可以：
1. 查看本使用说明文档
2. 检查浏览器控制台是否有错误信息
3. 尝试刷新页面或清除浏览器缓存
4. 联系技术支持团队

---

**祝您在AI攻防推演平台上学习愉快！** 🎉
