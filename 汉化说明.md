# CTF 擂台前端项目汉化说明

## 汉化概述

本项目是一个基于 Vue 3 + TypeScript 的 CTF（Capture The Flag）擂台前端应用。我已经完成了全面的中文汉化工作，将所有用户界面文本从英文翻译为中文。

## 汉化范围

### 1. 导航栏 (Navbar.vue)
- **CTF Platform** → **CTF 擂台**
- **Challenges** → **挑战题目**
- **Competitions** → **比赛竞技**
- **Profile** → **个人资料**
- **Login** → **登录**
- **Logout** → **退出登录**

### 2. 首页 (Home.vue)
- **Welcome to CTF Battle Platform** → **欢迎来到 CTF 擂台平台**
- **Master your cybersecurity skills through challenges and competitions** → **通过挑战和竞赛提升您的网络安全技能**
- **Practice Challenges** → **练习挑战**
- **Live Competitions** → **实时竞赛**
- **Track Progress** → **进度追踪**
- **Start Practice** → **开始练习**
- **Sign Up Now** → **立即注册**

### 3. 认证页面 (Auth.vue)
- **Login** / **Sign Up** → **登录** / **注册**
- **Email** → **邮箱**
- **Password** → **密码**
- **Confirm Password** → **确认密码**
- **Enter your email** → **请输入您的邮箱**
- **Enter your password** → **请输入您的密码**
- **Confirm your password** → **请再次输入密码**
- **Don't have an account?** → **还没有账户？**
- **Already have an account?** → **已有账户？**
- **Passwords do not match** → **密码不匹配**
- **Authentication failed** → **认证失败**

### 4. 挑战页面 (Challenges.vue)
- **CTF Challenges** → **CTF 挑战题目**
- 分类翻译：
  - **Web Security** → **Web 安全**
  - **Cryptography** → **密码学**
  - **Reverse Engineering** → **逆向工程**
  - **Binary Exploitation** → **二进制漏洞**
  - **Forensics** → **取证分析**
  - **All** → **全部**
- 难度等级：
  - **Easy** → **简单**
  - **Medium** → **中等**
  - **Hard** → **困难**
- **Deploy Challenge** → **部署挑战**
- **Challenge environment is being prepared...** → **挑战环境正在准备中...**
- **Connection Details** → **连接详情**
- **Enter flag (e.g., CTF{flag})** → **输入 flag (例如: CTF{flag})**
- **Submit Flag** → **提交 Flag**
- **Close** → **关闭**
- **pts** → **分**

### 5. 比赛页面 (Competitions.vue)
- **CTF Competitions** → **CTF 竞赛**
- 筛选选项：
  - **Status** → **状态**
  - **Format** → **格式**
  - **All** → **全部**
  - **Upcoming** → **即将开始**
  - **Ongoing** → **进行中**
  - **Completed** → **已结束**
  - **Attack/Defense** → **攻防对抗**
  - **King of the Hill** → **山丘之王**
- 比赛详情：
  - **Format** → **格式**
  - **Start** → **开始时间**
  - **End** → **结束时间**
  - **Team Size** → **队伍规模**
  - **Prize Pool** → **奖金池**
  - **Register Now** → **立即报名**
  - **Registered** → **已报名**
- 报名表单：
  - **Register for** → **报名参加**
  - **Team Name** → **队伍名称**
  - **Team Members** → **队伍成员**
  - **Enter your team name** → **请输入队伍名称**
  - **Enter member email** → **请输入成员邮箱**
  - **Add Team Member** → **添加队伍成员**
  - **Register Team** → **注册队伍**
  - **Cancel** → **取消**

### 6. 个人资料页面 (Profile.vue)
- 统计信息：
  - **Solved Challenges** → **已解决挑战**
  - **Total Points** → **总积分**
  - **Global Rank** → **全球排名**
  - **Completion Rate** → **完成率**
- **Recent Activity** → **最近活动**
- **points** → **分**

## 技术细节

### 日期格式化
- 将日期格式化从 `'en-US'` 改为 `'zh-CN'`，以显示中文日期格式

### CSS 样式调整
- 更新了难度等级的 CSS 类名，从英文（easy, medium, hard）改为中文（简单, 中等, 困难）

### 示例数据汉化
- 更新了所有示例挑战和比赛的标题、描述等内容
- 将示例活动记录翻译为中文

## 项目启动

项目已成功启动，运行在：
- **本地地址**: http://localhost:5174/
- **开发服务器**: Vite v5.4.19

## 汉化完成状态

✅ 所有用户界面文本已完成汉化
✅ 表单验证消息已汉化
✅ 错误提示信息已汉化
✅ 状态显示已汉化
✅ 日期格式已本地化
✅ CSS 样式已适配中文内容
✅ 示例数据已汉化

项目现在完全支持中文界面，用户可以使用中文进行所有操作。
