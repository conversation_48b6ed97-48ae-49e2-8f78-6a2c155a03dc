# AI 攻防推演平台 - 类型系统总结

## 📋 概述

本文档总结了AI攻防推演平台的完整TypeScript类型系统，包括数据模型定义、工具函数、常量和适配器。

## 🏗️ 类型系统架构

### 核心文件结构
```
src/types/
├── index.ts          # 主要类型定义和导出
├── adapters.ts       # 类型适配器和转换函数
├── utils.ts          # 类型工具函数
└── constants.ts      # 常量定义
```

## 📊 主要数据模型

### 1. 用户相关类型

#### User - 用户主体
```typescript
interface User {
  id: UUID
  email: string
  name: string
  avatar?: string
  joinDate: Timestamp
  role: 'admin' | 'user' | 'observer'
  preferences: UserPreferences
  statistics: UserStatistics
}
```

**关键特性**：
- 完整的用户偏好设置
- 详细的统计信息
- 技能等级评估
- 成就系统

#### UserStatistics - 用户统计
- 推演会话统计
- 技能等级分布
- 成就和排名
- 历史表现数据

### 2. 场景配置类型

#### ScenarioConfig - 场景配置
```typescript
interface ScenarioConfig {
  id: UUID
  name: string
  description: string
  template: ScenarioTemplate
  topology: NetworkTopology
  businessEnvironment: BusinessEnvironment
  vulnerabilityProfile: VulnerabilityProfile
  objectives: ScenarioObjective[]
  constraints: ScenarioConstraints
  metadata: ScenarioMetadata
}
```

**关键特性**：
- 模板化场景生成
- 复杂网络拓扑定义
- 业务环境模拟
- 漏洞配置文件
- 目标和约束系统

#### NetworkTopology - 网络拓扑
- 节点和连接定义
- 子网和安全区域
- 服务和漏洞配置
- 位置和状态管理

### 3. AI Agent 类型

#### AIAgent - AI代理
```typescript
interface AIAgent {
  id: UUID
  name: string
  type: 'attacker' | 'defender'
  model: AIModel
  configuration: AIAgentConfiguration
  status: AIAgentStatus
  capabilities: AICapability[]
  performance: AIPerformanceMetrics
}
```

**关键特性**：
- 攻击/防御角色定义
- AI模型配置
- 策略和战术系统
- 性能监控
- 伦理约束

#### AIAgentConfiguration - AI配置
- 攻击性、隐蔽性、持久性参数
- 警觉性、响应速度、覆盖范围参数
- 策略选择和适应规则
- 风险容忍度设置

### 4. 推演会话类型

#### SimulationSession - 推演会话
```typescript
interface SimulationSession {
  id: UUID
  name: string
  scenarioId: UUID
  participants: SessionParticipant[]
  configuration: SessionConfiguration
  timeline: SimulationTimeline
  status: SessionStatus
  results: SessionResults
}
```

**关键特性**：
- 多参与者支持
- 实时时间轴管理
- 事件和里程碑追踪
- 详细结果分析
- 会话配置管理

#### SimulationEvent - 推演事件
- 攻击/防御事件记录
- 事件源和目标
- 严重程度和状态
- 影响评估
- 证据收集

### 5. 评估报告类型

#### AssessmentReport - 评估报告
```typescript
interface AssessmentReport {
  id: UUID
  sessionId: UUID
  type: 'individual' | 'team' | 'comparative' | 'longitudinal'
  overview: ReportOverview
  skillAssessment: SkillAssessment
  performanceAnalysis: PerformanceAnalysis
  vulnerabilityAnalysis: VulnerabilityAnalysis
  recommendations: ReportRecommendation[]
}
```

**关键特性**：
- 多维度技能评估
- 性能分析
- 漏洞统计
- 智能推荐
- 趋势分析

#### SkillAssessment - 技能评估
- 技能等级和分数
- 强项和弱项分析
- 学习路径推荐
- 认证准备度评估

### 6. 决策日志类型

#### DecisionLog - 决策日志
```typescript
interface DecisionLog {
  id: UUID
  sessionId: UUID
  agentId: UUID
  timestamp: Timestamp
  type: 'strategic' | 'tactical' | 'operational' | 'reactive'
  decision: Decision
  context: DecisionContext
  reasoning: DecisionReasoning
  outcome: DecisionOutcome
}
```

**关键特性**：
- AI决策过程记录
- 推理逻辑追踪
- 上下文信息
- 结果评估
- 反馈机制

## 🔧 工具函数系统

### 数据验证函数
- `validateUser()` - 用户数据完整性验证
- `validateScenarioConfig()` - 场景配置验证
- `validateNetworkNode()` - 网络节点验证
- `validateSimulationSession()` - 推演会话验证

### 数据转换函数
- `scoreToLevel()` - 分数转等级
- `timestampToRelative()` - 时间戳转相对时间
- `secondsToReadable()` - 秒数转可读格式
- `bytesToReadable()` - 字节转可读大小

### 数据生成函数
- `generateUUID()` - 生成唯一标识符
- `generateTimestamp()` - 生成时间戳
- `generateRandomScore()` - 生成随机分数

### 数据过滤和排序
- `sortByTimestamp()` - 按时间排序
- `sortByScore()` - 按分数排序
- `filterActiveSessions()` - 过滤活跃会话
- `filterByDateRange()` - 按日期范围过滤

### 统计计算函数
- `calculateAverageScore()` - 计算平均分
- `calculateSuccessRate()` - 计算成功率
- `calculateSkillDistribution()` - 计算技能分布
- `calculateTrend()` - 计算趋势

## 📚 常量系统

### 用户相关常量
- `USER_ROLES` - 用户角色
- `USER_THEMES` - 主题选项
- `USER_LANGUAGES` - 语言选项
- `ACHIEVEMENT_RARITIES` - 成就稀有度

### 场景相关常量
- `SCENARIO_CATEGORIES` - 场景分类
- `SCENARIO_DIFFICULTIES` - 难度等级
- `NETWORK_TOPOLOGIES` - 网络拓扑类型
- `NODE_TYPES` - 节点类型
- `VULNERABILITY_SEVERITIES` - 漏洞严重程度

### AI相关常量
- `AI_AGENT_TYPES` - AI代理类型
- `AI_STRATEGIES` - AI策略
- `AI_ACTION_TYPES` - AI行为类型
- `AI_ERROR_TYPES` - AI错误类型

### 推演相关常量
- `SESSION_MODES` - 会话模式
- `SESSION_STATUSES` - 会话状态
- `EVENT_TYPES` - 事件类型
- `EVENT_SEVERITIES` - 事件严重程度

### 报告相关常量
- `REPORT_TYPES` - 报告类型
- `SKILL_LEVELS` - 技能等级
- `RECOMMENDATION_PRIORITIES` - 推荐优先级
- `TREND_DIRECTIONS` - 趋势方向

## 🔄 适配器系统

### 向后兼容性
- `LegacyUser` - 旧版用户类型
- `LegacyScenarioConfig` - 旧版场景配置
- `LegacySimulationSession` - 旧版推演会话

### 转换函数
- `adaptLegacyUser()` - 旧版用户转新版
- `adaptUserToLegacy()` - 新版用户转旧版
- `adaptLegacyScenarioConfig()` - 场景配置转换

### 类型守卫
- `isValidUUID()` - UUID验证
- `isValidTimestamp()` - 时间戳验证
- `isValidScore()` - 分数验证
- `isValidPercentage()` - 百分比验证

## 🎯 类型安全特性

### 强类型约束
- `UUID` - 唯一标识符类型
- `Timestamp` - 时间戳类型
- `Score` - 分数类型 (0-100)
- `Percentage` - 百分比类型 (0-100)

### 联合类型
- 状态枚举类型
- 角色和权限类型
- 事件和行为类型

### 泛型支持
- `APIResponse<T>` - 通用API响应
- `PaginatedResponse<T>` - 分页响应
- 过滤和排序函数泛型

## 📈 性能优化

### 类型推断优化
- 智能类型推断
- 条件类型使用
- 映射类型优化

### 内存效率
- 接口继承减少重复
- 可选属性合理使用
- 联合类型优化

## 🔒 安全考虑

### 数据验证
- 输入数据类型检查
- 范围验证
- 格式验证

### 错误处理
- 类型安全的错误处理
- 详细错误信息
- 错误代码常量

## 🚀 扩展性设计

### 模块化架构
- 独立的类型模块
- 清晰的依赖关系
- 易于扩展的接口

### 版本兼容性
- 向后兼容的适配器
- 渐进式类型升级
- 版本标识支持

## 📝 使用示例

### 基本类型使用
```typescript
import { User, ScenarioConfig, generateUUID } from '@/types'

const user: User = {
  id: generateUUID(),
  email: '<EMAIL>',
  name: '测试用户',
  // ... 其他属性
}
```

### 工具函数使用
```typescript
import { calculateAverageScore, formatTimestamp } from '@/types'

const scores: Score[] = [85, 92, 78]
const average = calculateAverageScore(scores)
const timeStr = formatTimestamp(new Date().toISOString())
```

### 常量使用
```typescript
import { SESSION_STATUSES, AI_STRATEGIES } from '@/types'

const status = SESSION_STATUSES.RUNNING
const strategy = AI_STRATEGIES.ADAPTIVE
```

## 🎉 总结

这个类型系统为AI攻防推演平台提供了：

1. **完整的类型覆盖** - 涵盖所有业务领域
2. **强类型安全** - 编译时错误检查
3. **良好的开发体验** - 智能提示和自动补全
4. **高度可维护性** - 清晰的结构和文档
5. **向后兼容性** - 平滑的迁移路径
6. **扩展性** - 易于添加新功能

通过这个类型系统，开发团队可以更安全、更高效地开发和维护AI攻防推演平台。
